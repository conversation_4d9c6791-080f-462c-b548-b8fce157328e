.contact-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    display: flex;
    padding: 35px;
    width: 100%;

    .is-list-structure & {
        margin-bottom: 20px;
        width: 793px;

        @include breakpoint(medium down) {
            width: 100%;
        }

        @media screen and (max-width: 1060px) {
            flex-direction: column;
        }
    }

    .is-list-equipments &,
    .is-list-training-institutions &,
    .is-list-institutions &,
    .is-list-services-directory &,
    .is-list-directory-of-activity-zones & {
        margin-bottom: 20px;
        width: 100%;

        @include breakpoint(medium down) {
            width: 94%;
        }

        @media screen and (max-width: 1060px) {
            flex-direction: column;
        }
    }

    &.is-structure {
        background-color: $color-3--1;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
    }

    .avatar-image {
        @include size(180px);
    }

    &__picture {
        display: block;
        flex-shrink: 0;
        margin-right: 24px;

        @include breakpoint(medium down) {
            margin-right: 29px;
            max-width: 200px;
        }

        @include breakpoint(small down) {
            margin: 0 auto 20px;
        }
    }

    &__content {
        flex-grow: 1;
        min-width: 1%;

        @include breakpoint(small down) {
            max-width: 480px;
            text-align: center;

            .is-list-structure & {
                width: 320px;
            }
        }
    }

    &__theme {
        @include font(var(--typo-1), 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        display: block;
        margin: 0 0 21px;
        text-transform: uppercase;

        @include breakpoint(medium down) {
            font-size: 1.4rem;
            margin-bottom: 13px;
        }

        @include breakpoint(small down) {
            font-size: 1.2rem;
        }
    }

    &__title {
        @include font(var(--typo-1), 2.2rem, var(--fw-bold));
        color: $color-black;
        line-height: calc(40 / 35);
        margin: 0;
        position: relative;
        top: 8px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: calc(24 / 22);
        }

        @include breakpoint(small down) {
            font-size: 2rem;
            line-height: calc(24 / 20);
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        @include breakpoint(small down) {
            font-size: 2rem;
            line-height: calc(24 / 20);
            max-width: 220px;
        }

        .underline {
            @include multiline-underline($size: 2px);
        }
    }

    &__function {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: calc(22 / 16);
        margin: 0 0 6px;

        &.is-main {
            color: var(--color-1--1);
            font-size: 1.8rem;
            font-weight: var(--fw-bold);
            line-height: calc(22 / 18);
        }
    }

    &__content-info {
        display: flex;
        justify-content: space-between;
        margin-top: 24px;

        .is-list-structure &,
        .is-list-equipments &,
        .is-list-training-institutions &,
        .is-list-institutions &,
        .is-list-services-directory &,
        .is-list-directory-of-activity-zones & {
            flex-direction: column;
        }

        @include breakpoint(medium down) {
            flex-direction: column;
            margin-top: 30px;
        }

        @include breakpoint(small down) {
            padding: 0 5px;
        }
    }

    &__details,
    &__infos {
        .infos {
            > *:last-child {
                margin-bottom: 0;
            }

            a {
                display: inline-flex;

                @include fa-icon-style(false) {
                    left: -33px;
                }
            }
        }

        a {
            position: relative;
            z-index: 42;
        }
    }

    &__details {

        @include breakpoint(medium down) {
            margin-bottom: 28px;
            padding: 0;
        }

        @include breakpoint(small down) {
            margin-bottom: 0;
        }
    }

    &__infos {
        align-items: flex-start;
        display: flex;
        flex-shrink: 0;
        flex-wrap: wrap;
        gap: 4px;
        width: 395px;

        &.is-list-structure-filter {
            flex-direction: column !important;
            width: 100% !important;
        }

        &.is-single-structure-links {
            flex-direction: row !important;
            gap: 7px;
            width: 100% !important;

            @include breakpoint(small down) {
                flex-direction: column !important;
            }

            .btn {
                background-color: var(--color-1--2);
                color: $color-white;
            }
        }

        .is-structure & {
            flex-direction: column;
            width: fit-content;

            .is-list-structure & {
                flex-direction: row;
                width: 370px;

                @include breakpoint(small down) {
                    flex-direction: column;
                    width: 100%;
                }
            }

            @include breakpoint(small down ,true) {
                align-items: center;
                width: 100%;
            }
        }

        @include breakpoint(small down , true) {
            align-items: center;
            flex-direction: column;
            flex-wrap: nowrap;
            width: 100%;
        }
    }

    &__infos-item {
        &:not(:last-child) {
            margin-bottom: 5px;
        }

        .is-structure & {
            .btn {
                background-color: var(--color-1--1);
                color: $color-white;
            }
        }
    }

    .is-width-66 & {
        @include breakpoint(large) {
            #{$this}__theme {
                font-size: 1.4rem;
                letter-spacing: 0;
                margin-bottom: 21px;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                    letter-spacing: 2.16px;
                    margin-bottom: 8px;
                }
            }

            #{$this}__title {
                font-size: 2.2rem;
                line-height: calc(24 / 22);
            }

            #{$this}__content-info {
                flex-direction: column;
                margin-top: 16px;
            }

            #{$this}__details {
                margin-bottom: 4px;
            }
        }
    }

    .is-width-33 & {
        @include breakpoint(large) {
            align-items: center;
            flex-direction: column;

            #{$this}__picture {
                margin: 0 auto 20px;
            }

            #{$this}__content {
                max-width: 480px;
                text-align: center;
            }

            #{$this}__theme {
                font-size: 1.4rem;
                letter-spacing: 0;
                margin-bottom: 21px;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                    letter-spacing: 2.16px;
                    margin-bottom: 8px;
                }
            }

            #{$this}__title {
                font-size: 2rem;
                line-height: calc(24 / 20);
            }

            #{$this}__content-info {
                flex-direction: column;
                margin-top: 30px;
            }

            #{$this}__details {
                margin-bottom: 28px;
            }

            #{$this}__infos {
                align-items: center;
                width: 100%;
            }
        }
    }

    &__socials {
        margin-bottom: 19px;

        .is-single-structure & {
            margin-bottom: 7px;
            margin-top: 42px;
        }

        .social-links {
            &.is-floating {
                flex-direction: row;
                gap: 5px;

                @include breakpoint(small down , true) {
                    justify-content: center;
                }

                .social-links__link {
                    @include size(40px);
                    background-color: var(--color-1--2);
                    color: $color-white;

                    @include on-event {
                        background-color: var(--color-1--1);
                    }
                }
            }
        }
    }
}

.list-contact-small {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 40px 29px;

    &__item {
        width: 200px;
    }
}

.contact-item-small {
    display: flex;
    flex-direction: column;
    width: 200px;

    &__picture {
        @include size(200px);
        margin-right: 0;

        .contact-item__picture {
            margin-right: 0;
        }
    }

    &__content {
        display: flex;
        flex-direction: column;

        &__name {
            @include font (var(--typo-1), 2.2rem, var(--fw-bold));
            color: $color-black;
            margin: 0 0 5px;
            width: 177px;
        }

        &__function {
            @include font (var(--typo-1), 1.6rem, var(--fw-normal));
        }
    }
}

.contact-item-secondary {
    @include font (var(--typo-1), 1.1rem, var(--fw-normal));
    align-items: center;
    color: $color-3--4;
    display: flex;
    gap: 10px;

    &__icon {
        display: flex;
        justify-content: center;
        width: 20px;
    }

    &__name {
        color: $color-3--4;
    }
}
