.event-carousel {
    margin: 0 -12px;
    position: relative;

    @include breakpoint(medium down) {
        padding-left: 40px;
        padding-right: 40px;
    }

    @include breakpoint(small down) {
        max-width: 550px;
        padding-left: 30px;
        padding-right: 30px;
    }

    &__control {
        @include absolute(50%);
        background: none;
        border: 0;
        cursor: pointer;
        overflow: hidden;
        padding: 0;
        transform: translateY(-50%);

        @include fa-icon-style(false) {
            color: $color-3--3;
            font-size: 2.4rem;
            font-weight: var(--fw-bold);
        }

        &.is-prev {
            left: 0;

            @include breakpoint(medium down) {
                left: 35px;
            }

            @include breakpoint(small down) {
                left: 10px;
            }
        }

        &.is-next {
            right: 0;

            @include breakpoint(medium down) {
                right: 35px;
            }

            @include breakpoint(small down) {
                right: 10px;
            }
        }
    }

    &__item {
        height: auto;
        padding: 0 12px;

        @include breakpoint(small down) {
            padding-left: 5px;
            padding-right: 5px;
        }
    }
}
