{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/telecharger/telecharger.njk' import Telecharger -%}
{% from 'views/utils/utils.njk' import svg, wrapper with context %}
{% from 'components/popup-close-btn/popup-close-btn.njk' import PopupCloseBtn %}
{% from 'views/core-components/secondary.njk' import Date %}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/core-components/button.njk' import Button -%}

{#
    FaqItem template.
#}

{%- macro FaqItem(tag = 'dl', itemClass = '') -%}
    <{{ tag }} class="faq-item {{ itemClass }}">
    <dt class="faq-item__toggle-wrapper">
        <button type="button" class="faq-item__toggle">
            <span class="far fa-angle-down faq-item__toggle-icon" aria-hidden="true"></span>
            <span class="faq-item__toggle-category">{{ lorem(1, 'words') }}</span>
            <span class="faq-item__toggle-text">{{ lorem(2) }}</span>
        </button>
    </dt>
    <dd class="faq-item__block rte" aria-hidden="true">
        <div class="faq-item__wrapper">
            <p class="faq-item__response">Réponse :</p>
            <p class="faq-item__text">{{ lorem(5) }}</p>
            <p class="publication is-primary faq-item__publication">
                <span>Publié le</span>
                <time datetime="{{ datetime }}">27 Janvier 2021</time>
                <span>- Mise à jour le</span>
                <time datetime="{{ datetime }}">13/01/2022</time>
            </p>
        </div>
    </dd>
    </{{ tag }}>
{%- endmacro -%}

{#
    FaqList template.
    @param {string} itemClass - item class modifier.
    @param {number} count - items count.
#}
{%- macro FaqList(
    itemClass = 'list__item has-mb-1',
    count = 12
) -%}
    <dl class="list is-columns-1">
        {% for item in range(0, count) %}
            {{ FaqItem(tag = 'div', itemClass = itemClass) }}
        {% endfor %}
    </dl>
{%- endmacro -%}

{#
    FaqContent template.
    Template for faq on page-content.
    @param {string} titleText - section title
    @param {number} itemsCount - count of subpages items
#}
{%- macro FaqContent(
    titleText = 'FAQ',
    itemsCount = 3,
    moreButton = true,
    itemClass = 'list__item has-mb-1'
) -%}
    {% call Section(className = 'faq-section', container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ FaqList(itemClass = itemClass, count = itemsCount) }}
        </div>
        {% if moreButton %}
            <div class="section__more-links more-links">
                    {{ Link(
                        href = kGlobalLinks.listNews,
                        text = 'TOUTES LES QUESTIONS',
                        className = 'btn is-small-blue',
                        icon = 'far fa-long-arrow-right'
                    ) }}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContentIdeasBoxItem template.
#}

{%- macro ContentIdeasBoxItem(tag = 'dl', itemClass = '') -%}
    {%- set uid_1 = Helpers.unique() -%}
    {%- set uid_2 = uid_1 + Helpers.unique() -%}
    <{{ tag }} class="faq-item {{ itemClass }}">
    <dt class="faq-item__toggle-wrapper">
        <button type="button" class="faq-item__toggle">
            <span class="far fa-angle-down faq-item__toggle-icon" aria-hidden="true"></span>
            <span class="faq-item__toggle-category">{{ lorem(1, 'words') }}</span>
            <span class="faq-item__toggle-text">Lorem ipsum dolor sit amet consetetur sadipscing nonumy eirmod tempor</span>
            <p class="publication is-primary faq-item__publication">
                <span>Publié le</span>
                <time datetime="{{ datetime }}">27/11/2021</time>
                <span>- Mise à jour le</span>
                <time datetime="{{ datetime }}">13/01/2022</time>
            </p>
        </button>
    </dt>
    <dd class="faq-item__block ideas-box-content" aria-hidden="true">
        <div class="faq-item__wrapper">
            <div class="faq-item__tags">
                {% set tags = ['tag1', 'tag2', 'tag3'] %}
                {% for tag in tags %}
                    <span class="faq-item__tag">{{ tag | capitalize }}</span>
                {% endfor %}
            </div>
            <p class="faq-item__text">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea ipsum dolor sit amet.</p>
            <div class="faq-item__documents">
                {{Telecharger(count = 3, readBtn = false)}}
            </div>
            <div class="faq-item__response">
                <p class="faq-item__response-title">C.U. ARRAS :</p>
                <p class="faq-item__response-text">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea ipsum dolor sit amet.</p>
            </div>
            <div class="faq-item__vote">
                {% if range(1, 9) | random > 5 %}
        {{ Link(
            className = 'btn is-small',
            href = '#',
            icon = '',
            text = 'Vous avez voté pour ce projet',
            disabled = true
        ) }}
    {% else %}
        {{ Link(
            className = 'btn is-small',
            href = '#',
            icon = '',
            text = 'JE VOTE',
            disabled = false
        ) }}
    {% endif %}

                    <div class="faq-item__vote-results">
                        <div class="faq-item__vote-results-item">
                            <p class="faq-item__vote-results-item-title">Nombre de votes: </p>
                            <p class="faq-item__vote-results-item-text">4</p>
                        </div>
                        <div class="faq-item__vote-results-item">
                            <p class="faq-item__vote-results-item-date">Date limite :</p>
                            <p class="faq-item__vote-results-item-date">12/02/2025</p>
                        </div>
                    </div>

                    
                    <div class="faq-item__comments">
                        {% call wrapper(
                            className = 'main-carousel-item__link',
                            href = '#faq-popup-add-comment_'+uid_1,
                            attrs = {
                                'data-fancybox': "timeline_"+uid_1,
                                'data-small-btn': "false",
                                'data-toolbar': "false",
                                'data-content': "Timeline",
                                'aria-haspopup': "dialog",
                                'data-fancybox-body-class': "is-carousel-opened"
                            }
                        ) %}
                            <a href="#" class="comment-link">ajouter un commentaire</a>
                        {% endcall %}
                    </div>

                    <div class="faq-item__comments">
                        {% call wrapper(
                            className = 'main-carousel-item__link',
                            href = '#faq-popup_'+uid_2,
                            attrs = {
                                'data-fancybox': "timeline_"+uid_2,
                                'data-small-btn': "false",
                                'data-toolbar': "false",
                                'data-content': "Timeline",
                                'aria-haspopup': "dialog",
                                'data-fancybox-body-class': "is-carousel-opened"
                            }
                        ) %}
                            <a href="#" class="comment-link">Voir les 204 commentaires</a>
                        {% endcall %}
                    </div>
            </div>
            <div class="faq-item__voteEnd">
                <p class="faq-item__voteEnd-title">Vote terminé</p>
            </div>
        </div>
        <div class="main-carousel-item__popup" hidden="hidden">
            {{ CarouselPopupAddComment(uid_1) }}
        </div>
        <div class="main-carousel-item__popup" hidden="hidden">
            {{ CarouselPopup(uid_2) }}
        </div>
    </dd>
    </{{ tag }}>
{%- endmacro -%}


{#
    IdeasBoxList template.
    @param {string} itemClass - item class modifier.
    @param {number} count - items count.
#}
{%- macro IdeasBoxList(
    itemClass = 'list__item has-mb-1',
    count = 12
) -%}
    <dl class="list is-columns-1">
        {% for item in range(0, count) %}
            {{ ContentIdeasBoxItem(tag = 'div') }}
        {% endfor %}
    </dl>
{%- endmacro -%}

{%- macro CommentsItem() -%}
    <div class="comment-item">
        <div class="comment-item__user-info">
            <div class="comment-item__avatar">
                {{ svg('icons/avatar' + range(1, 9) | random, 40, 40) }}
            </div>
            <div class="comment-item__text">
                <p class="comment-item__text-date"><span class="name">MOI</span> - Publié le 01/03/2021</p>
                <p class="comment-item__text-description">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur elitr, sed diam nonumy eirmod tempor invidunt ut</p>
            </div>
        </div>
{%- endmacro -%}

{%- macro CarouselPopup(uid) -%}
    <div class="popup faq-popup" id="faq-popup_{{ uid }}">
        <div class="faq-popup__wrapper">
            {{ PopupCloseBtn(
            modifier = 'btn is-only-icon is-inverted is-small',
            ghostText = true,
            tooltip = 'Fermer la popin'
        ) }}
            <div class="faq-popup__content-wrap">
                <div class="faq-popup__title">
                    <h3 class="faq-popup__title-text">Titre de la question</h3>
                </div>
                <div class="faq-popup__content">
                    {% for item in range(0, 5) %}
                        {{ CommentsItem() }}
                    {% endfor %}
                </div>

            </div>
    </div>
{%- endmacro -%}

{%- macro CommentsForm() -%}
    <div class="comments-form">
        <div class="comments-form__content">
            {# The Form wrapper, same SCSS approach as before #}
            {%- call Form.FormWrapper(
                legend = false,
                className = "js-validator-form"
            ) -%}

            {{ Form.FormField(
                type = "textarea",
                label = "Votre commentaire :",
                required = true
            ) }}
            <div class="event-message__info">
            {{ Form.RadioCheckbox(
                label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données.<a href="#"> Cliquez ici pour les consulter</a>',
                required = true,
                disableRequiredLabel = false
            ) }}
             </div>
             <div class="event-message__captcha">
                ###PASTE CAPCHA HERE
            </div>
            {{ Button(
            className = "btn is-primary is-md-small has-mt-3",
            type = "submit",
            text = "postuler mon commentaire"
            ) }}
            {%- endcall -%}
        </div>
    </div>
{%- endmacro -%}


{%- macro CarouselPopupAddComment(uid) -%}
    <div class="popup faq-popup" id="faq-popup-add-comment_{{ uid }}">
        <div class="faq-popup__wrapper is-comments">
            {{ PopupCloseBtn(
            modifier = 'btn is-only-icon is-inverted is-small',
            ghostText = true,
            tooltip = 'Fermer la popin'
        ) }}
            <div class="faq-popup__title">
                <h3 class="faq-popup__title-text">Titre de l'enrigistrement de commentaire</h3>
            </div>
            <div class="faq-popup__content-wrap">
                {{ CommentsForm() }}
            </div>
    </div>
{%- endmacro -%}


