{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/utils/utils.njk' import svg with context -%}

{% macro AccountBoxItem(
    titleText = 'Associez votre compte à FranceConnect',
    animation = false,
    separator = false
) %}
    <div class="account-box-item {{ 'js-account-scroll-animation' if animation }}">
        <div class="account-box-item__text">
            <h2 class="account-box-item__title">{{ titleText }}</h2>
            <p class="account-box-item__description">FranceConnect c'est la solution proposée par l’État pour sécuriser et simplifier votre connexion aux services en ligne.</p>
        </div>
        <div class="account-box-item__button-wrapper">
            <a
                class="account-box-item__btn"
                target="_blank"
                rel="noopener"
                href="https://app.franceconnect.gouv.fr/en-savoir-plus"
            >
                {{ svg('account/france-connect-image', 282, 74) }}
            </a>
            <div class="account-box-item__info">
                <a class="account-box-item__link" href="https://app.franceconnect.gouv.fr/en-savoir-plus" title="qu'est-ce que franceconnect ? (Lien externe)" target="_blank" rel="noopener">Qu'est-ce que FranceConnect ?</a>
            </div>
        </div>
        {% if separator %}
            <div class="account-box-item__separator">Ou</div>
        {% endif %}
    </div>
{% endmacro %}

{% macro AccountBox() %}
    {% call Section(className = 'account-box', container = false) %}
        {{ AccountBoxItem() }}
    {%- endcall -%}
{% endmacro %}
