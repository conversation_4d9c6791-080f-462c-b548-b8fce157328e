import { objectToQueryString } from '../utils/transform.utils';
import { setCookiePlaceholder } from '../utils/cookie.utils';

declare let tarteaucitron;

/**
 * Custom youtube cookie service for tarteaucitron plugin.
 */
export default class DailymotionCookie {
    public constructor() {
        try {
            this.init();
        } catch (error) {
            console.error(error);
        }
    }

    /**
     * Set service cookies according dependencies
     */
    public update(): void {
        // Add update logic here.
    }

    /**
     * Add event listeners for service.
     */
    private addListeners(): void {
        // Add listeners here.
    }

    /**
     * Handle service items if cookies accepted.
     */
    private acceptedHandler(): void {
        const elements = [...document.querySelectorAll('.js-dailymotion-player-stratis')];

        elements.forEach(elem => {
            const elemAttrs = {
                videoID: tarteaucitron.getElemAttr(elem,'videoID'),
                width: tarteaucitron.getElemAttr(elem,'width'),
                height: tarteaucitron.getElemAttr(elem,'height'),
            };

            if (!elemAttrs.videoID) {
                return '';
            }

            const videoParams = objectToQueryString({
                info: tarteaucitron.getElemAttr(elem,'showinfo'),
                autoPlay: tarteaucitron.getElemAttr(elem,'autoplay'),
            });

            // eslint-disable-next-line max-len
            elem.innerHTML = `<iframe type="text/html" loading="lazy" src="//www.dailymotion.com/embed/video/${elemAttrs.videoID + videoParams}" frameborder="0" allowfullscreen></iframe>`;
        });
    }

    /**
     * Handle service items if cookies denied.
     */
    private deniedHandler(): void {
        const id = 'stratis-dailymotion';

        const elements = [...document.querySelectorAll('.js-dailymotion-player-stratis')];

        elements.forEach(elem => {
            const placeholder = setCookiePlaceholder(id, elem, true);
            elem.appendChild(placeholder);
        });
    }

    /**
     * Init service.
     */
    private init(): void {
        tarteaucitron.services['stratis-dailymotion'] = {
            key: 'stratis-dailymotion',
            type: 'video',
            name: 'Dailymotion',
            readmoreLink: 'https://tarteaucitron.io/fr/service/dailymotion/',
            uri: 'https://www.dailymotion.com/legal/privacy',
            needConsent: true,
            cookies: ['ts', 'dmvk', 'hist', 'v1st', 's_vi'],
            js: () => this.acceptedHandler(),
            fallback: () => this.deniedHandler(),
        };

        this.addListeners();
    }
}
