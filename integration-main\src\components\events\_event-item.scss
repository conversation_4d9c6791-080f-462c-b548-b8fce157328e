.event-item {
    $this: &;

    @extend %link-block-context;
    cursor: pointer;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto;
    max-width: 384px;

    @include breakpoint(medium down) {
        align-items: center;
        max-width: 100%;
    }

    @include breakpoint(small down) {
        align-items: flex-start;
        flex-direction: row-reverse;
    }

    .is-list-with-filter & {
        @media (max-width: 1248px) {
            align-items: flex-start;
            flex-direction: row-reverse;
        }

        @include breakpoint(small down) {
            justify-content: center;
        }
    }

    // styles for widget/sidebar on CP
    .events-widget & {
        #{$this} {
            &__content {
                @include breakpoint(large only) {
                    padding-inline: 35px;
                }
            }
        }
    }

    &__image {
        display: block;

        @include breakpoint(medium down) {
            @include min-size(189px, 267px);
            @include size(189px, 267px);
        }

        @include breakpoint(small down) {
            @include min-size(133px, 188px);
            @include size(133px, 188px);
        }

        .is-list-with-filter & {
            @media (max-width: 1248px) {
                @include min-size(133px, 188px);
                @include size(133px, 188px);
            }
        }

        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__date {
        margin-top: -46px;
        max-width: 157px;

        @include breakpoint(medium down) {
            margin: -40px;
            max-width: 127px;
        }

        @include breakpoint(small down) {
            max-width: 122px;
        }

        .is-list-with-filter & {
            @media (max-width: 1248px) {
                max-width: 122px;
            }
        }
    }

    &__content {
        padding: 12px 10px 20px;

        @include breakpoint(medium) {
            text-align: center;
        }

        @include breakpoint(medium down) {
            padding: 48px 20px 20px;
        }

        @include breakpoint(small down) {
            padding: 20px 19px 10px;
        }

        .is-list-with-filter & {
            @media (max-width: 1248px) {
                padding: 20px 19px 10px;
            }
        }
    }

    &__content-wrap {
        align-items: center;
        display: flex;
        flex-direction: column;

        @include breakpoint(small down) {
            padding-bottom: 40px;
        }

        .is-list-with-filter & {
            @media (max-width: 1248px) {
                padding-bottom: 40px;
            }
        }
    }

    &__title {
        line-height: 1.3;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }

        .is-list-with-filter & {
            @include breakpoint(small down) {
                width: 168px;
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        @include font(null, 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-bottom: 5px;
    }

    &__time-place {
        @include breakpoint(small down) {
            margin-top: 5px;
        }

        .is-list-with-filter & {
            @media (max-width: 1248px) {
                margin-top: 5px;
            }
        }

        .time-place {
            &__item {
                color: $color-black;

                @include breakpoint(medium) {
                    justify-content: center;
                }
            }
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }
}
