.drop-area {
    $this: &;
    margin: 30px 0;

    &__block {
        @include trs;
        @include size(100%, 200px);
        align-items: center;
        background-color: var(--global-input-bg);
        border: 1px solid var(--global-input-border-color);
        border-radius: var(--global-input-border-radius);
        cursor: pointer;
        display: flex;
        justify-content: center;
        position: relative;

        @include breakpoint(small down) {
            height: 150px;
        }

        &::before {
            @include absolute(10%, null, null, 2.5%);
            @include size(95%, 80%);
            @include trs;
            background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='5' ry='5' stroke='%23ca000a' stroke-width='4' stroke-dasharray='6%2c 18' stroke-dashoffset='12' stroke-linecap='square'/%3e%3c/svg%3e");
            border-radius: 5px;
            content: '';
            opacity: 0;
        }

        &.is-target::before {
            opacity: 1;
        }
    }

    &__content {
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-self: center;
    }

    &__title {
        color: var(--color-1--1);
        font-size: 17px;
        margin-top: 8px;
        text-transform: uppercase;
    }

    svg {
        overflow: visible;

        & > g > g {
            @include trs;

            &:last-of-type {
                transform: translate(295px, 303px);
            }
        }
    }

    &:hover,
    &.is-highlighted {
        svg > g > g:last-of-type {
            transform: translate(295px, 295px);
        }
    }

    &.is-highlighted {
        #{$this}__block::before {
            opacity: 1;
        }
    }
}
