import initTemplateHandlers from '@core/map-helpers/map-template';
import StratisTooltip from '@core/core-components/stratis-tooltip';
import StratisMapFilter from '@core/core-components/stratis-map-filter';

// Import StratisMap
import { documentReady, happyLogger } from '@core/utils/common.utils';
// import StratisMapLayerSwitcher from '@components/map/js/plugins/map-layer-switcher';
import { isElementIntoView } from './utils/dom.utils';
import StratisMapUserGeoposition from '@components/map/js/plugins/map-user-geoposition';
import calculatePictureWidth from '@core/core-base/calculate-picture-width';

documentReady(() => {
    happyLogger('MAP BUNDLE LOADED!');

    // Init map template functionallity
    initTemplateHandlers();

    // Init large map.
    isElementIntoView('.js-map', async () => {

        const userGeoposition = new StratisMapUserGeoposition('.js-user-geoposition');
        const { default: StratisMap } = await import('@components/map/js/map');
        const largeMap = new StratisMap('.js-map');

        // Initialize dynamic map filter
        const _mapFilter = new StratisMapFilter(largeMap);

        // uncomment strings below for satellite view
        // const layerSwitcher = new StratisMapLayerSwitcher('.js-map-layers-select');
        // largeMap.addPlugin(layerSwitcher);
        largeMap.addPlugin(userGeoposition);
    });

    // Custom logic on map ready event.
    // document.addEventListener('mapReady', evt => {});
    // Custom logic when map popup is shown
    document.addEventListener('mapPopupShow', () => {
        StratisTooltip.update();
        calculatePictureWidth();
        window.addEventListener('resize', calculatePictureWidth);
    });
    // Custom logic when map popup is hidden
    // document.addEventListener('mapPopupHide', evt => {});
});
