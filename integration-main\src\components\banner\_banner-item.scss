.banner-item {
    $this: &;

    @extend %link-block-context;
    align-items: stretch;
    display: flex;
    flex-direction: row-reverse;
    position: relative;
    z-index: 1;

    @include breakpoint(small down) {
        flex-direction: column-reverse;
    }

    @include breakpoint(small down, true) {
        flex-direction: column-reverse;

        &.is-reverse {
            flex-direction: column;
        }
    }

    &__image {
        width: 50%;

        @include breakpoint(small down) {
            width: 100%;
        }

        @include breakpoint(small down, true) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100%);

            @include breakpoint(small down) {
                max-height: 250px;
            }

            @include breakpoint(xsmall down) {
                max-height: 135px;
            }
        }
    }

    &__content {
        @include size(50%, null);
        padding: 40px 100px 40px 65px;

        @include breakpoint(medium down) {
            padding: 21px 60px 21px 30px;
        }

        @include breakpoint(small down) {
            padding: 28px 50px 28px 34px;
            width: 100%;
        }

        .is-inverted & {
            color: $color-white;
        }
    }

    @include fa-icon-style(false) {
        @include trs;
        @include absolute(null, 58px, 53px, null);
        font-size: 3rem;
        font-weight: var(--fw-normal);

        @include breakpoint(medium down) {
            @include absolute(null, 32px, 28px, null);
            font-size: 1.6rem;
        }

        @include breakpoint(small down) {
            @include absolute(null, 24px, 30px, null);
        }
    }

    &__title {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        display: inline;
        line-height: 1.2;

        @include breakpoint(medium down) {
            font-size: 2.4rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }

        .underline {
            .is-inverted & {
                @include multiline-underline();
            }
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus-visible {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &.is-reverse {
        flex-direction: row;

        @include breakpoint(small down) {
            flex-direction: column;
        }

        @include fa-icon-style(false) {
            right: calc(50% + 58px);

            @include breakpoint(medium down) {
                right: calc(50% + 32px);
            }

            @include breakpoint(small down) {
                bottom: 285px;
                right: 24px;
            }

            @include breakpoint(xsmall down) {
                bottom: 170px;
            }
        }
    }

    .is-width-33 & {
        @include breakpoint(large) {
            flex-direction: column-reverse;

            &.is-reverse {
                flex-direction: column;

                @include fa-icon-style(false) {
                    bottom: 195px;
                }
            }

            #{$this}__content {
                padding: 37px 59px 37px 41px;
                width: 100%;
            }

            #{$this}__title {
                font-size: 2.4rem;
                line-height: calc(28 / 24);
            }

            @include fa-icon-style(false) {
                bottom: 35px;
                font-size: 1.9rem;
                right: 28px;
            }
        }
    }
}
