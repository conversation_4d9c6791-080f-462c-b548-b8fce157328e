import { key } from 'ally.js/when/_when';
import { tabFocus } from 'ally.js/maintain/_maintain';
import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import StratisFactory from '@core/core-base/stratis-factory';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import LocalStorage from '@core/core-base/local-storage';
import { getNextFocusableElement } from '@core/utils/a11y.utils';

interface IFlashInfoOptions extends IGlobalOptions<FlashInfo> {
    storage: string;
    expireTime?: number;
}
/**
 * Class represents FlashInfo object.
 */
class FlashInfo extends StratisElementAbstract {
    public options: IFlashInfoOptions = {
        storage: 'flash-info',
        classList: {
            closeButton: 'js-flash-info-close',
            titleText: 'flash-info__title-text',
            hidden: 'is-hidden',
            visible: 'is-visible',
            popup: 'is-popup',
            bodyOpenedHelper: 'js-flash-info-overflow',
            bannerOpenedHelper: 'js-flash-info-banner-opened',
            headerClass: 'header',
        },
        DOMElements: {},
        expireTime: 30, // Days
        dataset: {},
        onInit: () => null,
    };

    private closeButton: HTMLElement | null = null;

    private focusTarget: HTMLElement | null = null;

    private readonly storage: LocalStorage;

    private readonly $closeHandler = this.closeHandler.bind(this);

    public constructor(element: HTMLElement, options: Partial<IFlashInfoOptions>) {
        super(element);
        this.setOptions(this.options, options);

        this.storage = new LocalStorage(this.options.storage);

        this.init();
    }

    /**
     * Create FlashInfo instances (FlashInfo factory).
     * @param {string} selector - selector for flash info item.
     * @param {IFlashInfoOptions} options - flash info options.
     */
    static create(selector: string, options?: Partial<IFlashInfoOptions>): FlashInfo[] | null {
        return StratisFactory.create(selector, options || {}, FlashInfo);
    }

    /**
     * Initialize FlashInfo instance.
     */
    protected init(): void {
        this.created = true;

        this.focusTarget = getNextFocusableElement(document.body, document.body) as HTMLElement;

        this.hideItem();
        this.initEvents();
        this.checkRecordStatus();
    }

    /**
     * Initialize instance events.
     */
    protected initEvents(): void {
        this.closeButton = this.element.querySelector(`.${this.options.classList.closeButton}`);

        if (this.closeButton) {
            this.closeButton.addEventListener('click', this.$closeHandler);
        }
    }

    /**
     * Hide FlashInfo.
     */
    private hideItem(): void {
        const isPopupOpened = hasClass(document.body, this.options.classList.bodyOpenedHelper);
        const isBannerOpened = hasClass(document.body, this.options.classList.bannerOpenedHelper);

        removeClass(this.element, this.options.classList.visible);
        addClass(this.element, this.options.classList.hidden);

        if (hasClass(this.element, this.options.classList.popup)) {
            if (this.$focusHandler) {
                this.$focusHandler.disengage();
            }

            if (this.$keyHandler) {
                this.$keyHandler.disengage();
            }

            if (isPopupOpened && this.focusTarget) {
                this.focusTarget.focus();
                removeClass(document.body, this.options.classList.bodyOpenedHelper);
            }
        } else if (isBannerOpened && !isPopupOpened && this.focusTarget) {
            const header = document.querySelector(`${this.options.classList.headerClass}`) as HTMLElement;
            this.focusTarget = getNextFocusableElement(this.element, header) as HTMLElement;
            this.focusTarget.focus();
            removeClass(document.body, this.options.classList.bannerOpenedHelper);
        }
    }

    /**
     * Show FlashInfo.
     */
    private showItem(): void {
        removeClass(this.element, this.options.classList.hidden);
        addClass(this.element, this.options.classList.visible);

        if (hasClass(this.element, this.options.classList.popup)) {
            const closeButton: HTMLElement | null = this.element.querySelector(`.${this.options.classList.closeButton}`);
            closeButton?.focus();

            addClass(document.body, this.options.classList.bodyOpenedHelper);

            this.$focusHandler = tabFocus({
                context: this.element,
            });

            this.$keyHandler = key({
                context: document.body,
                escape: () => this.closeHandler(),
            });
        } else {
            addClass(document.body, this.options.classList.bannerOpenedHelper);
        }
    }

    /**
     * Close handler.
     */
    private closeHandler(): void {
        const date = new Date();
        date.setTime(date.getTime() + (this.options.expireTime || 30) * 24 * 60 * 60 * 1000);

        const expireTime = date.getTime();

        this.storage.setItem({
            [this.element.id]: expireTime,
        });

        this.hideItem();
    }

    /**
     * Check record in storage and hide it if it has expiring time.
     */
    private checkRecordStatus(): void {
        const storageRecord = this.storage.getObj()[this.element.id];

        if (storageRecord && storageRecord > Date.now()) {
            this.hideItem();
        } else {
            this.storage.setItem({
                [this.element.id]: 0,
            });

            this.showItem();
        }
    }
}

export default FlashInfo;
