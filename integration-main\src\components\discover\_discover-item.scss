.discover-item {
    $this: &;

    align-items: center;
    display: flex;
    margin-bottom: 30px;
    min-height: 437px;

    @include breakpoint(medium down) {
        align-items: normal;
        min-height: 100%;
    }

    @include breakpoint(small down) {
        flex-direction: column-reverse;
        justify-content: flex-end;
    }

    &.is-image-left {
        @include breakpoint(medium) {
            flex-direction: row-reverse;
        }

        #{$this}__content-wrapper::before {
            @include absolute(50%, calc(100% - 1px), null, auto);
            transform: translateY(-50%) rotate(180deg);

            @include breakpoint(small down) {
                display: none;
            }
        }

        #{$this}__content-wrapper::after {
            @include absolute(null, 100%, 0, null);
            @include size(50px, 100%);
            content: '';
            pointer-events: none;
        }
    }

    .is-full-width & {
        align-items: normal;
        margin-bottom: 0;

        #{$this} {
            &__content-wrapper {
                left: 0;
                min-height: 100%;

                @include breakpoint(small down) {
                    margin: -35px auto 0;
                    width: calc(100% - 40px);
                }

                &::before {
                    content: none;
                }
            }

            &__content {
                margin: 50px 66px;

                @include breakpoint(small down) {
                    margin: 35px 42px 42px;
                }
            }

            &__category {
                @include breakpoint(large only) {
                    margin-bottom: 20px;
                }
            }

            &__link {
                @include breakpoint(large only) {
                    @include min-size(100px, 63px);
                    font-size: 1.3rem;
                    padding: 1.7em 3.3em;
                }
            }

            &__image {
                width: 50%;

                @include breakpoint(small down) {
                    width: 100%;
                }
            }
        }
    }

    &__image {
        width: 60%;

        @include breakpoint(medium down) {
            width: 50%;
        }

        @include breakpoint(small down) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__content-wrapper {
        align-items: center;
        background-color: var(--color-1--1);
        display: flex;
        justify-content: center;
        left: 54px;
        min-height: 400px;
        position: relative;
        width: 50%;
        z-index: 2;

        @include breakpoint(medium down) {
            left: 0;
            min-height: 100%;
        }

        .is-image-left & {
            left: -54px;

            &::after {
                @include absolute(null,null , 0, 100%);
            }

            @include breakpoint(medium down) {
                left: 0;
            }
        }

        @include breakpoint(small down) {
            width: 100%;
        }

        &::before {
            @include absolute(50%, null, null, calc(100% - 1px));
            @include size(28px, 94px);
            background-color: var(--color-1--1);
            clip-path: polygon(0 0, 0 100%, 100% 50%);
            content: '';
            transform: translateY(-50%);

            @include breakpoint(medium down) {
                @include size(21px, 68px);
            }

            @include breakpoint(small down) {
                display: none;
            }
        }

        &::after {
            @include absolute(null, 100%, 0, null);
            @include size(55px, 100%);
            background-color: var(--color-1--1);
            content: '';
            pointer-events: none;

            @include breakpoint(medium down) {
                display: none;
            }
        }
    }

    &__content {
        left: -48px;
        margin: 29px 0 33px;
        padding-left: 56px;
        padding-right: 20px;
        position: relative;
        top: 15px;

        @include breakpoint(medium down) {
            position: static;
        }

        @include breakpoint(small down) {
            padding: 0;
        }

        .is-image-left & {
            position: static;

            @include breakpoint(small down) {
                padding: 0;
            }
        }

        .is-full-width & {
            padding-left: 0;
            position: static;
        }

        @include breakpoint(small down) {
            margin: 35px 29px 35px;
            text-align: center;
        }
    }

    &__category {
        color: $color-white;
        margin-bottom: 15px;
        text-transform: uppercase;

        @include breakpoint(large only) {
            font-size: 1.8rem;
        }

        @include breakpoint(small down) {
            margin-bottom: 5px;
        }
    }

    &__title {
        color: $color-white;
        font-size: 3rem;
        font-weight: var(--fw-bold);
        max-width: 433px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    & &__link {
        border: 1px solid $color-white;
        margin-top: 24px;

        @include breakpoint(medium down) {
            margin-top: 20px;
        }
    }
}
