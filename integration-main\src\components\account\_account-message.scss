.account-message {
    $this: &;

    padding-top: 8px;

    &__wrapper {
        @include line-decor(100px, 16px, 'before');
        align-items: center;
        background-color: $color-white;
        box-shadow: 0 0 20px rgba($color-black, 0.1);
        display: flex;
        flex-direction: column;
        max-width: 610px;
        padding: 70px;
        position: relative;
        width: 100%;

        @include breakpoint(small down) {
            padding: 50px 20px;
        }

        &::before {
            @include absolute(0, null, null, 50%);
            transform: translate(-50%, -50%);
        }
    }

    &__title {
        @include font(var(--typo-1), 2.8rem, var(--fw-black));
        @include focus-outline;
        color: var(--color-1--1);
        line-height: 1.25;
        margin: 0 0 15px;
        text-align: center;

        @include breakpoint(medium only) {
            font-size: 2.6rem;
            line-height: calc(30 / 26);
        }
    }

    &__small-description {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        display: block;
        line-height: 1.25;
        margin: 0 0 30px;
        text-align: center;
    }

    &__icon {
        @include size(100%, 80px);
        margin-bottom: 15px;

        svg {
            @include size(100%);
            fill: $color-green;
        }

        &.is-red svg {
            fill: $color-red;
        }
    }

    &__subtitle {
        @include font(var(--typo-1), 1.6rem, var(--fw-bold));
        line-height: 1.25;
        margin: 0 0 30px;
        text-align: center;
    }

    &__description {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-black;
        line-height: 1.25;
        margin: 0 0 50px;
        text-align: center;

        &:last-child {
            margin-bottom: 0 !important;
        }

        p:not(:last-child) {
            margin-bottom: 25px;
        }
    }

    &__main-link {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: var(--color-1--1);
        display: block;
        line-height: 1.25;
        margin: 30px 0 0;
        text-align: center;

        @include on-event() {
            text-decoration: none;
        }
    }

    &__info {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-black;
        line-height: calc(18 / 14);
        text-align: center;
    }

    &__form {
        --global-input-fz: 1.6rem;
        --global-input-min-height: 45px;
        --global-input-padding: 0.4em 1.15em;

        margin: 0;
        max-width: 435px;
        width: 100%;

        @include breakpoint(medium down) {
            max-width: 385px;
        }

        .form__field-wrapper {
            margin-bottom: 9px;
        }

        input::placeholder {
            font-size: inherit;
        }

        .btn {
            width: 100%;
        }
    }
}
