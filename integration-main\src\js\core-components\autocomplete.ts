import { key } from 'ally.js/when/_when';
import l10n from '@core/lang/locallang';
import StratisElementAbstract from '@core/abstract/stratis-element.abstract';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import StratisFactoryMixin from '@core/mixins/stratis-factory.mixin';
import OnInit from '@core/decorators/init-method.decorator';
import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
import { clearLineOfDiacritics, getUniqId } from '@core/utils/common.utils';
import {
    getFirstFocusableElement,
    getLastFocusableElement,
    getNextFocusableElement,
    getPrevFocusableElement, setTabindex,
} from '@core/utils/a11y.utils';
import { convertObjectToQueryString } from '@core/utils/converters.utils';

interface IAutocompleteOptions extends IGlobalOptions<Autocomplete> {
    delay: number;
    calculateWidth: boolean;
    calculatePosition: boolean;
}

/**
 * StratisAutocomplete plugin.
 */
class Autocomplete extends StratisElementAbstract {
    public options: IAutocompleteOptions = {
        classList: {
            inputClass: 'js-autocomplete-input', // class of input in which the search is conducted
            wrapperClass: 'js-autocomplete-result-wrapper',
            listClass: 'js-autocomplete-result-list',
            itemClass: 'js-autocomplete-result-item',
            linkClass: 'js-autocomplete-result-link',
            textClass: 'js-autocomplete-result-text',
            btnClear: 'js-autocomplete-input-clear',
            loader: 'loader-grow',
            isVisible: 'is-visible',
            isActive: 'is-active',
        },
        delay: 300, // delay before starting the search
        calculateWidth: true, // if "true" - calculate the width of the block with the results
        calculatePosition: true, // if "true" - calculate position for blocks with results
        dataset: {
            'json-path': false, // path to json file
            'min-chars': 3, // minimum number of characters to search
            'min-elements': 5, // minimum number of results found necessary to display the button
            'max-elements': 6, // minimum number of results found necessary to display the button
            'mark-coincidence': false, // if "on" - enable highlight matches in words
        },
        DOMElements: {},
    };

    private data: any = null;
    private checkMarkup = false;
    private lastValue = '';
    private timeOut: any = null;
    private inputField: HTMLInputElement | null = null;
    private buttonClear: HTMLInputElement | null = null;
    private resultsWrapper: HTMLInputElement | null = null;
    private resultsList: HTMLInputElement | null = null;
    private loader: HTMLInputElement | null = null;

    // Bindings
    private readonly $upDownHandler = this.upDownHandler.bind(this);
    private readonly $enterHandler = this.enterHandler.bind(this);
    private readonly $escHandler = this.escHandler.bind(this);
    private readonly $tabHandler = this.tabHandler.bind(this);
    private readonly $homeHandler = this.homeHandler.bind(this);
    private readonly $endHandler = this.endHandler.bind(this);
    private readonly $clearInput = this.clearInput.bind(this);

    public constructor(selector: HTMLElement, options: Partial<IAutocompleteOptions>) {
        super(selector, options);
        if (!this.created) {
            this.init();
        }
    }

    /**
     * Hide the block with the results.
     */
    public hideResults(): void {
        this.timeOut = clearTimeout(this.timeOut);
        this.unselectListItem();
        removeClass(this.element, this.options.classList.isVisible);
        setTabindex(this.resultsList!, -1);
        this.inputField!.setAttribute('aria-expanded', 'false');
        this.inputField!.setAttribute('aria-activedescendant', '');
    }

    protected createEvents(): void {
        super.createEvents([
            [this.inputField, 'keyup', this.keyUpInput],
            [this.element, 'click', this.clickHandler],
            [document, 'click', this.outsideHandler],
            [window, 'resize', this.resizeAndPosition],
        ]);
    }

    private getOtherFilters(title: string): Record<string, string> {
        const otherFilters: Record<string, string> = {};
        const form = document.querySelector('form.views-exposed-form') as HTMLFormElement;

        if (!form) {
            return otherFilters;
        }

        const elements = form.querySelectorAll('input, select, textarea');
        elements.forEach(el => {
            this.processElement(el, title, otherFilters);
        });

        return otherFilters;
    }

    private processElement(el: any, title: string, otherFilters: Record<string, string>): void {
        const { name, value: val } = el;

        if (!name || name === title) {
            return;
        }

        if (el.type === 'checkbox') {
            this.processCheckbox(el, name, val, otherFilters);
        } else if (this.shouldAddElementValue(el, val)) {
            otherFilters[name] = val;
        }
    }

    private processCheckbox(el: HTMLInputElement, name: string, val: string, otherFilters: Record<string, string>): void {
        if (el.checked && (el.getAttribute('aria-checked') === 'true' || el.checked)) {
            otherFilters[name] = val;
        }
    }

    private shouldAddElementValue(el: any, val: string): boolean {
        return val !== '' && el.type !== 'hidden';
    }

    /**
     * By clicking on the input, checking the condition.
     * @param {object} e - event object.
     */
    private keyUpInput(e): void {
    const { target } = e;
    const { 'min-chars': minChars } = this.options.dataset;
    const keysExclusion = [9, 13, 16, 27, 35, 36, 37, 38, 39, 40];

    this.timeOut = clearTimeout(this.timeOut);

    if (keysExclusion.includes(e.which)) {
        return;
    }

    this.lastValue = clearLineOfDiacritics(target.value);
    target.setAttribute('data-autocomplete-lastValue', this.lastValue);

    const valueLength = target.value.length;
    const title = target.name;

    if (valueLength >= minChars) {
        this.buttonClear!.addEventListener('click', this.$clearInput, false);

        addClass(this.buttonClear!, this.options.classList.isVisible);
        addClass(this.loader!, this.options.classList.isActive);
        this.timeOut = setTimeout(() => {
            const filters = this.getOtherFilters(title);
            this.loadData(this.lastValue, title, filters);
        }, this.options.delay);

        this.centerElementInInput(this.buttonClear);
        this.centerElementInInput(this.loader);
    } else if (valueLength === 0) {
        removeClass(this.buttonClear!, this.options.classList.isVisible);
        removeClass(this.loader!, this.options.classList.isActive);
    } else {
        this.hideResults();
        removeClass(this.loader!, this.options.classList.isActive);
        }
    }


    /**
     * Set the element to the center of the input.
     * @param element - element to be aligned.
     * @private
     */
    private centerElementInInput(element): void {
        if (this.inputField && element) {
            element.style.top = `${this.inputField.offsetTop + this.inputField.offsetHeight / 2 - element.offsetHeight / 2}px`;
            element.style.bottom = 'auto';
        }
    }

    /**
     * The value from input is sent to search for results.
     * @param {string} symbols - inputs value.
     * @param {string} title - inputs name.
     */
    private async loadData(symbols: string, title: string, filters: Record<string, string> = {}): Promise<void> {
    const { 'json-path': path } = this.options.dataset;

    // Add the main search input
    const params: Record<string, string> = {
        [title]: symbols,
        ...filters,
    };

    const finalURL = `${path}${convertObjectToQueryString(params)}`;
    console.log(finalURL);

    try {
        const response = await fetch(finalURL, { method: 'GET' });

        if (response.ok) {
            const results = await response.json();
            this.data = Object.keys(results).map((key): any => results[key]);
            this.addItems();
            this.showResults();
            removeClass(this.loader!, this.options.classList.isActive);
        }
    } catch (error) {
        console.warn('JSON file with data not found.');
        console.warn(error);
    }
}


    /**
     * Clicking past we hide the results
     * @param {object} event - event object.
     */
    private outsideHandler({ target }): void {
        const isCorrectClick = this.inputField!.parentElement!.contains(target);

        if (!isCorrectClick) {
            this.hideResults();
            removeClass(this.loader!, this.options.classList.isActive);
        }
    }

    /**
     * Show the block with the results
     */
    private showResults(): void {
        this.resizeAndPosition();
        addClass(this.element, this.options.classList.isVisible);
        this.inputField!.setAttribute('aria-expanded', 'true');
    }

    /**
     * Clean input.
     */
    private clearInput(): void {
        this.lastValue = '';
        this.inputField!.value = this.lastValue;
        this.inputField!.setAttribute('data-autocomplete-lastValue', this.lastValue);
        this.hideResults();
        removeClass(this.buttonClear!, this.options.classList.isVisible);
        this.inputField!.focus();
    }

    /**
     * Find out the input width and add it for the block with the results.
     */
    private resizeAndPosition(): void {
        const { calculateWidth, calculatePosition } = this.options;

        if (calculateWidth) {
            this.resultsWrapper!.style.width = window.getComputedStyle(this.inputField!).getPropertyValue('width');
        }

        if (calculatePosition) {
            this.resultsWrapper!.style.top = `${this.inputField!.offsetTop + this.inputField!.offsetHeight}px`;
        }
    }

    /**
     * Add items with results.
     */
    private addItems(): void {
        const { linkClass, textClass, itemClass } = this.options.classList;
        const { 'mark-coincidence': markCoincidence, 'max-elements': maxElements } = this.options.dataset;
        this.resultsList!.innerHTML = '';

        this.data.forEach((item: any, index: number): void => {
            const itemId = getUniqId();

            if (index < Number(maxElements)) {
                const regex = markCoincidence === 'on' ? new RegExp(this.lastValue, 'ig') : '';
                const link = typeof item.title !== 'undefined'
                    ? `<a class="${linkClass}" href="${item.title.href}"><span>${item.title.text.replace(regex, '<mark>$&</mark>')}</span><span>${item.type.text}</span></a>`
                    : `<p class="${textClass}" onclick="" tabindex="0"><span>${item.name.replace(regex, '<mark>$&</mark>')}</span></p>`;
                const itemTemplate = `<li class="${itemClass}" role="option" id="${itemId}">${link}</li>`;
                this.resultsList!.insertAdjacentHTML('beforeend', itemTemplate);
            }
        });
    }

    /**
     * By pressing the up or down key find prev/next focusable element and select it.
     * @param {object} e - event object.
     */
    private upDownHandler(e): void {
        e.preventDefault();
        const selectedItem = this.getSelectedListItem();
        const currentItem = selectedItem ? selectedItem.firstElementChild : this.inputField;
        const targetItem = e.which === 38
            ? getPrevFocusableElement(currentItem as HTMLElement, this.resultsWrapper!)
            : getNextFocusableElement(currentItem as HTMLElement, this.resultsWrapper!);

        this.unselectListItem();
        this.setInputValue(targetItem);
    }

    /**
     * Set input value.
     * @param {object} element - Focusable Element.
     */
    private setInputValue(element): void {
        if (element) {
            element.parentElement.setAttribute('aria-selected', 'true');
            const { id } = element.parentElement;
            const elementSpan = element.querySelector('span');
            const { tagName } = element;
            const shouldGetSpanText = tagName.toLowerCase() === 'a' || tagName.toLowerCase() === 'p';

            this.inputField!.setAttribute('aria-activedescendant', id);
            this.inputField!.value = shouldGetSpanText && elementSpan
                ? elementSpan.innerText
                : this.lastValue;
        }
    }

    /**
     * By click close the result and focus on input.
     */
    private clickHandler(e): void {
        const { target } = e;
        const { textClass } = this.options.classList;

        if (hasClass(target, textClass) || hasClass(target.parentElement, textClass)) {
            e.preventDefault();
            const targetSpan = target.querySelector('span') || target.parentElement.querySelector('span');
            this.inputField!.value = targetSpan.innerText;
            this.lastValue = targetSpan.innerText;
            this.hideResults();
            this.inputField!.focus();

            // Trigger submission if the field is named "Keywords".
            if (this.inputField && hasClass(this.inputField, 'js-autocomplete-input')) {
                const form = this.inputField.closest('form');
                if (form) {
                    const submitBtn = form.querySelector('button[type="submit"]') as HTMLButtonElement;
                    if (submitBtn) {
                        submitBtn.click(); // more AJAX-friendly than requestSubmit()
                    }
                }
            }
        }
    }

    /**
     * By pressing the enter key set input value and close the results.
     */
    private enterHandler(e): void {
        const selectedItem = this.getSelectedListItem();

        if (selectedItem) {
            e.preventDefault();
            const targetSpan = selectedItem.querySelector('span') as HTMLElement;
            this.inputField!.value = targetSpan.innerText;
            this.lastValue = targetSpan.innerText;
            this.hideResults();
        }
    }

    /**
     * By pressing the esc key close the result and focus on input.
     */
    private escHandler(event): void {
        const { isVisible } = this.options.classList;

        if (hasClass(this.element, isVisible)) {
            event.stopPropagation();
        }

        if (this.inputField!.getAttribute('data-autocomplete-lastValue')) {
            this.inputField!.value = this.lastValue;
        }

        this.hideResults();
        this.inputField!.focus();
    }

    /**
     * By pressing the tab key close the result and focus on the next or prev element.
     */
    private tabHandler(): void {
        if (this.inputField!.getAttribute('data-autocomplete-lastValue')) {
            this.inputField!.value = this.lastValue;
        }

        this.hideResults();
    }

    /**
     * By pressing the home key focus on the first list item.
     */
    private homeHandler(event): void {
        const selectedItem = this.getSelectedListItem();

        if (selectedItem) {
            event.preventDefault();
            const targetItem = getFirstFocusableElement(this.resultsWrapper!);

            this.unselectListItem();
            this.setInputValue(targetItem);
        }
    }

    /**
     * By pressing the end key focus on the last list item.
     */
    private endHandler(event): void {
        const selectedItem = this.getSelectedListItem();

        if (selectedItem) {
            event.preventDefault();
            const targetItem = getLastFocusableElement(this.resultsWrapper!);

            this.unselectListItem();
            this.setInputValue(targetItem);
        }
    }

    /**
     * Unselect list item.
     */
    private unselectListItem(): void {
        const selectedItem = this.getSelectedListItem();

        if (selectedItem) {
            selectedItem.removeAttribute('aria-selected');
        }
    }

    /**
     * Get selected list item.
     */
    private getSelectedListItem(): HTMLElement | undefined {
        const { itemClass } = this.options.classList;
        const items = [...this.resultsList!.querySelectorAll<HTMLElement>(`.${itemClass}`)];
        return items.find(el => el.hasAttribute('aria-selected'));
    }

    /**
     * Get targeted autocomplete elements
     */
    @OnInit()
    private getElements(): void {
        const { inputClass, wrapperClass, listClass, btnClear } = this.options.classList;
        this.inputField = this.element.querySelector<HTMLInputElement>(`.${inputClass}`);
        this.buttonClear = this.element.querySelector<HTMLInputElement>(`.${btnClear}`);
        this.resultsWrapper = this.element.querySelector<HTMLInputElement>(`.${wrapperClass}`);
        this.resultsList = this.element.querySelector<HTMLInputElement>(`.${listClass}`);
        this.loader = this.element.querySelector(`.${this.options.classList.loader}`);
    }

    /**
     * Create a wrapper for the results.
     */
    @OnInit()
    private addMarkup(): void {
        const { wrapperClass, listClass, btnClear } = this.options.classList;
        const listId = getUniqId();

        if (!this.checkMarkup && this.inputField) {
            this.inputField.setAttribute('autocomplete', 'off');
            const buttonClear = `<button type="button" class="${btnClear}"><span aria-hidden="true" class="${btnClear}-icon"></span><span class="${btnClear}-text">${l10n.btnClearText}</span></button>`;
            const markup = `<div class="${wrapperClass}"><ul id="${listId}" class="${listClass}" role="listbox" aria-label="${l10n.suggestions}"></ul></div>`;
            this.inputField.setAttribute('aria-controls', listId);
            this.inputField.insertAdjacentHTML('afterend', buttonClear);
            this.inputField.insertAdjacentHTML('afterend', markup);
            this.checkMarkup = true;
            this.getElements();
        }
    }

    @OnInit()
    private keyHandler(): void {
        this.$keyHandler = key({
            context: this.element,
            up: this.$upDownHandler,
            down: this.$upDownHandler,
            enter: this.$enterHandler,
            escape: this.$escHandler,
            tab: this.$tabHandler,
            'shift+tab': this.$tabHandler,
            home: this.$homeHandler,
            end: this.$endHandler,
        } as any);
    }
}

const AutocompleteFactory = StratisFactoryMixin<typeof Autocomplete, Autocomplete, IGlobalOptions<Autocomplete>>(Autocomplete);
export default AutocompleteFactory;
