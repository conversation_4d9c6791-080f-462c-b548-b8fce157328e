.consultations-register {
    align-items: center;
    background-color: var(--color-1--1);
    display: flex;
    justify-content: space-between;
    padding: 35px 70px;

    @include breakpoint(medium down) {
        flex-direction: column;
        padding: 30px 60px 44px;
    }

    @include breakpoint(small down) {
        padding: 30px 20px;
    }

    &__title {
        @extend %underline-context;
        @include font(var(--typo-1), 3.5rem, var(--fw-bold));
        color: $color-white;
        line-height: 4rem;
        margin: 0 26px 0 0;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
            line-height: 3.2rem;
            margin: 0 0 16px;
        }

        @include breakpoint(small down) {
            font-size: 2.4rem;
            line-height: 2.8rem;
        }

        .underline {
            @include multiline-underline($size: 2px);
            background-position-y: -4px;
            color: inherit;
        }
    }

    &__contacts {
        display: flex;
        margin: 0 -9px;

        @include breakpoint(small down) {
            flex-wrap: wrap;
            justify-content: center;
            margin: -7px;
        }

        .btn {
            margin: 0 9px;

            @include breakpoint(small down) {
                margin: 7px;
            }
        }
    }
}
