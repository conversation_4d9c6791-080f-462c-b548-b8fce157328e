{%- from 'views/partials/search/search-active.njk' import SearchActive -%}
{%- from 'views/partials/search/search-main.njk' import SearchMain -%}
{%- from 'views/partials/search/search-sorting.njk' import SearchSorting -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- import 'views/core-components/filter.njk' as Filter -%}
{%- from 'components/map-search/map-search.njk' import MapSearchWithFilters -%}

{%- macro SearchSidebarEvent() -%}
    <div id="tx-solr-search-functions" class="search-aside">
        <div class="search-filter-event">
            <div class="search-filter-event__wrapper" data-sd-content="search-filter">
                <div class="result-count">
                    <p class="nb_result">
                        <span class="nb_number js-map-search-count">XX</span> résultats
                    </p>
                    <label class="custom-radio-button">
                        <input type="radio" name="view-toggle" id="toggle-map" class="toggle-input" aria-checked="false">
                        <span class="radio-label">Carte</span>
                        <span class="circle"></span>
                    </label>
                </div>
                <div class="search-aside__group-wrapper">
                    {% call Filter.FilterWrapper(legend = false, ariaLabelText = false, formClassName = 'views-exposed-form') %}
                        {% call Filter.FieldsWrapper() %}
                            {% call Filter.FieldsGroup() %}
                                {{ MapSearchWithFilters() }}
                                {% call Filter.FilterField('col-xs-12') %}
                                    {{ Form.FormField(type= 'autocomplete', label = 'Mots-clés :', placeholder = 'Ecrire un ou plusieurs mots-clés..') }}
                                {% endcall %}
                                {% call Filter.FilterField('col-xs-12') %}
                                    {{ Form.FormField(type= 'autocomplete', label = 'Commune :', placeholder = 'Saisir une commune', wrapperModifier = 'is-commune') }}
                                {% endcall %}    
                                {{ Filter.ButtonsWrapper() }}
                                <div class="search-aside__group-thematique">
                                    <span class="search-aside__group-thematique-title">Thématiques</span>
                                    {{ Form.SelectList() }}
                                </div>
                                <div class="search-aside__group-thematique">
                                    <span class="search-aside__group-thematique-title search-aside__group-thematique-title-more">+ Affiner ma recherche</span>
                                    <div class="search-aside__group-of-extreme-search is-hidden-filters">
                                        {{ Form.SelectList() }}
                                        {% call Filter.FilterField('col-xs-12') %}
                                            {{ Form.FormField(type = 'select', label = 'Date', placeholder = '-Sélectionner une date-' , required = false) }}
                                        {% endcall %}
                                        {{ Form.DateRangePicker(
                                            label = 'Sélectionner une date',
                                            fromLabel = 'Du',
                                            toLabel = 'Au',
                                            required = true,
                                            wrapperClass = 'period-date',
                                            placeholder = '',
                                            required = false
                                        ) }}
                                        {{ Form.RadioCheckbox(label = 'Voir les évènements archivés' , required = false) }}
                                    </div>
                                </div>
                            {% endcall %}
                        {% endcall %}
                    {% endcall %}
                </div>
            </div>
        </div>
    </div>
{%- endmacro -%}


{%- macro SearchSidebarStructure() -%}
    <div id="tx-solr-search-functions" class="search-aside">
        <div class="search-filter-event">
            <div class="search-filter-event__wrapper" data-sd-content="search-filter">
                <div class="result-count">
                    <p class="nb_result">
                        <span class="nb_number">XX</span> résultats
                    </p>
                    <label class="custom-radio-button">
                        <input type="radio" name="view-toggle" id="toggle-map" class="toggle-input" aria-checked="false">
                        <span class="radio-label">Carte</span>
                        <span class="circle"></span>
                    </label>
                </div>
                <div class="search-aside__group-wrapper">
                    {% call Form.FormWrapper() %}
                        {% call Filter.FieldsGroup() %}
                            {% call Filter.FilterField() %}
                                {{ Form.FormField(type= 'autocomplete', label = 'Mots-clés :', placeholder = 'Saisir le(s) mot(s)-clé(s)..') }}

                            {% endcall %}
                            {{ Filter.ButtonsWrapper() }}
                        {% endcall %}
                    {% endcall %}
                </div>
            </div>
        </div>
    </div>
{%- endmacro -%}

{%- macro SearchSidebarEquipments() -%}
    <div id="tx-solr-search-functions" class="search-aside">
        <div class="search-filter-event">
            <div class="search-filter-event__wrapper" data-sd-content="search-filter">
                <div class="result-count">
                    <p class="nb_result">
                        <span class="nb_number">XX</span> résultats
                    </p>
                    <label class="custom-radio-button">
                        <input type="radio" name="view-toggle" id="toggle-map" class="toggle-input" aria-checked="false">
                        <span class="radio-label">Carte</span>
                        <span class="circle"></span>
                    </label>
                </div>
                <div class="search-aside__group-wrapper">
                    {% call Form.FormWrapper() %}
                        {% call Filter.FieldsGroup() %}
                            {% call Filter.FilterField() %}
                                {{ Form.FormField(type= 'autocomplete', label = 'Mots-clés :', placeholder = 'Ecrire un ou plusieurs mots-clés..') }}
                            {% endcall %}
                            {{ Filter.ButtonsWrapper() }}
                            <div class="search-aside__group-thematique">
                                <span class="search-aside__group-thematique-title">Thématiques</span>
                                {{ Form.SelectList() }}
                            </div>
                            <div class="search-aside__group-thematique">
                                <span class="search-aside__group-thematique-title search-aside__group-thematique-title-more">+ Affiner ma recherche</span>
                                <div class="search-aside__group-of-extreme-search is-hidden-filters">
                                    {{ Form.SelectList() }}
                                    {{ Form.FormField(label = 'Commune :', placeholder = 'Saisir une commune', wrapperModifier = 'is-commune', required = false) }}
                                </div>
                            </div>
                        {% endcall %}
                    {% endcall %}
                </div>
            </div>
        </div>
    </div>
{%- endmacro -%}

{%- macro SearchSidebarTrainingInstitutions() -%}
    <div id="tx-solr-search-functions" class="search-aside">
        <div class="search-filter-event">
            <div class="search-filter-event__wrapper" data-sd-content="search-filter">
                <div class="result-count">
                    <p class="nb_result">
                        <span class="nb_number">XX</span> résultats
                    </p>
                    <label class="custom-radio-button">
                        <input type="radio" name="view-toggle" id="toggle-map" class="toggle-input" aria-checked="false">
                        <span class="radio-label">Carte</span>
                        <span class="circle"></span>
                    </label>
                </div>
                <div class="search-aside__group-wrapper">
                    {% call Form.FormWrapper() %}
                        {% call Filter.FieldsGroup() %}
                            {% call Filter.FilterField() %}
                                {{ Form.FormField(type= 'autocomplete', label = 'Mots-clés :', placeholder = 'Ecrire un ou plusieurs mots-clés..') }}
                            {% endcall %}
                            {{ Filter.ButtonsWrapper() }}
                            <div class="search-aside__group-thematique">
                               {{ Form.Multiselect(legend = 'Type d\'établissement', placeholder = '-Sélectionner-') }}
                            </div>
                            <div class="search-aside__group-thematique">
                                <span class="search-aside__group-thematique-title search-aside__group-thematique-title-more">+ Affiner ma recherche</span>
                                <div class="search-aside__group-of-extreme-search is-hidden-filters">
                                    {{ Form.Multiselect(legend = 'Diplômes', placeholder = '-Sélectionner-') }}
                                    {{ Form.Multiselect(legend = 'Domaines', placeholder = '-Sélectionner-') }}
                                    {{ Form.Multiselect(legend = 'Type de formation', placeholder = '-Sélectionner-') }}
                                    {{ Form.FormField(label = 'Commune', placeholder = '' , required = false) }}

                                </div>
                            </div>
                        {% endcall %}
                    {% endcall %}
                </div>
            </div>
        </div>
    </div>
{%- endmacro -%}

{%- macro SearchSidebarInstatutions() -%}
    <div id="tx-solr-search-functions" class="search-aside">
        <div class="search-filter-event">
            <div class="search-filter-event__wrapper" data-sd-content="search-filter">
                <div class="result-count">
                    <p class="nb_result">
                        <span class="nb_number">XX</span> résultats
                    </p>
                    <label class="custom-radio-button">
                        <input type="radio" name="view-toggle" id="toggle-map" class="toggle-input" aria-checked="false">
                        <span class="radio-label">Carte</span>
                        <span class="circle"></span>
                    </label>
                </div>
                <div class="search-aside__group-wrapper">
                    {% call Form.FormWrapper() %}
                        {% call Filter.FieldsGroup() %}
                            {% call Filter.FilterField() %}
                                {{ Form.FormField(type= 'autocomplete', label = 'Mots-clés :', placeholder = 'Ecrire un ou plusieurs mots-clés..') }}
                            {% endcall %}
                            {{ Filter.ButtonsWrapper() }}
                            <div class="search-aside__group-thematique">
                                <span class="search-aside__group-thematique-title">Thématiques</span>
                                {{ Form.SelectList() }}
                            </div>
                            <div class="search-aside__group-thematique">
                                <span class="search-aside__group-thematique-title search-aside__group-thematique-title-more">+ Affiner ma recherche</span>
                                <div class="search-aside__group-of-extreme-search is-hidden-filters">
                                    {{ Form.SelectList() }}
                                    {{ Form.FormField(label = 'Commune :', placeholder = 'Saisir une commune', wrapperModifier = 'is-commune', required = false) }}
                                </div>
                            </div>
                        {% endcall %}
                    {% endcall %}
                </div>
            </div>
        </div>
    </div>
{%- endmacro -%}

{%- macro SearchSidebarServicesDirectory() -%}
    <div id="tx-solr-search-functions" class="search-aside">
        <div class="search-filter-event">
            <div class="search-filter-event__wrapper" data-sd-content="search-filter">
                <div class="result-count">
                    <p class="nb_result">
                        <span class="nb_number">XX</span> résultats
                    </p>
                    <label class="custom-radio-button">
                        <input type="radio" name="view-toggle" id="toggle-map" class="toggle-input" aria-checked="false">
                        <span class="radio-label">Carte</span>
                        <span class="circle"></span>
                    </label>
                </div>
                <div class="search-aside__group-wrapper">
                    {% call Form.FormWrapper() %}
                        {% call Filter.FieldsGroup() %}
                            {% call Filter.FilterField() %}
                                {{ Form.FormField(type= 'autocomplete', label = 'Mots-clés :', placeholder = 'Ecrire un ou plusieurs mots-clés..') }}
                            {% endcall %}
                            {{ Filter.ButtonsWrapper() }}
                        {% endcall %}
                    {% endcall %}
                </div>
            </div>
        </div>
    </div>
{%- endmacro -%}

{%- macro SearchSidebarDirectoryOfActivityZones() -%}
    <div id="tx-solr-search-functions" class="search-aside">
        <div class="search-filter-event">
            <div class="search-filter-event__wrapper" data-sd-content="search-filter">
                <div class="result-count">
                    <p class="nb_result">
                        <span class="nb_number">XX</span> résultats
                    </p>
                    <label class="custom-radio-button">
                        <input type="radio" name="view-toggle" id="toggle-map" class="toggle-input" aria-checked="false">
                        <span class="radio-label">Carte</span>
                        <span class="circle"></span>
                    </label>
                </div>
                <div class="search-aside__group-wrapper">
                    {% call Form.FormWrapper() %}
                        {% call Filter.FieldsGroup() %}
                            {% call Filter.FilterField() %}
                                {{ Form.FormField(label = 'Mot-clés :', placeholder = 'Saisir le(s) mot(s)-clé(s)..' , required = false, type = 'autocomplete') }}
                            {% endcall %}
                            {{ Filter.ButtonsWrapper() }}
                            <div class="search-aside__group-thematique">
                                <span class="search-aside__group-thematique-title search-aside__group-thematique-title-more">+ Affiner ma recherche</span>
                                <div class="search-aside__group-of-extreme-search is-hidden-filters">
                                    {{ Form.FormField(label = 'Commune implantation', placeholder = '' , required = false) }}
                                    {{ Form.Multiselect(legend = 'principales activités', placeholder = '-Sélectionner-') }}
                                    {{ Form.Multiselect(legend = 'filières présentes', placeholder = '-Sélectionner-') }}

                                </div>
                            </div>
                        {% endcall %}
                    {% endcall %}
                </div>
            </div>
        </div>
    </div>
{%- endmacro -%}