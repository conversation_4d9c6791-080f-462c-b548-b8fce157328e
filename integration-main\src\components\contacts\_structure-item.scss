.contact-item {
    $this: &;

    &.is-structure {
        @include breakpoint(small down) {
            padding: 35px 10px;

            .is-list-structure &,
            .is-list-equipments &,
            .is-list-training-institutions &,
            .is-list-institutions &,
            .is-list-services-directory &,
            .is-list-directory-of-activity-zones & {
                margin: auto;
                width: 320px;
            }
        }

        #{$this}__picture {
            @include breakpoint(medium down) {
                margin-right: 30px;
                max-width: 200px;
            }

            @include breakpoint(small down) {
                margin: 0 auto 12px;
            }
        }

        #{$this}__content-top {
            @include breakpoint(small down) {
                padding: 0 30px;
            }
        }

        #{$this}__content-info {
            @include breakpoint(small down) {
                margin-top: 10px;
                padding: 0 23px;
            }
        }

        #{$this}__details {
            text-align: left;

            .is-width-66 & {
                max-width: 399px;
            }

            .infos:nth-child(2) {
                margin-top: 11px;

                @include breakpoint(small down) {
                    display: flex;
                    justify-content: center;
                    margin-top: 0;
                }
            }
        }

        .is-width-66 & {
            @include breakpoint(large) {
                #{$this}__picture {
                    left: -2px;
                    margin-right: 30px;
                    max-width: 200px;
                    position: relative;
                    top: 5px;
                }

                #{$this}__infos {
                    flex-direction: row;
                    flex-wrap: wrap;
                    margin: 0 -2.5px;
                    width: 400px;
                }

                #{$this}__infos-item {
                    margin: 0 2.5px 5px;
                }
            }
        }

        .is-width-33 & {
            @include breakpoint(large) {
                #{$this}__picture {
                    margin: 0 auto 20px;
                    max-width: 200px;
                }

                #{$this}__content-info {
                    margin-top: 10px;
                    padding: 0 30px;
                }

                #{$this}__details {
                    .infos:nth-child(2) {
                        display: flex;
                        justify-content: center;
                        margin-top: 20px;
                    }
                }
            }
        }
    }
}
