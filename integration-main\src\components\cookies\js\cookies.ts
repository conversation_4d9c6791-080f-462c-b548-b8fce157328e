import { Constructor } from '@core/interfaces/base-types';
import { TarteaucitronSettingsModel, StratisCookiesModel } from './models/cookies.model';
import FancyboxCookie from './services/fancybox';
import YoutubeCookie from './services/youtube';
import VimeoCookie from './services/vimeo';
import DailymotionCookie from './services/dailymotion';
import { hasClass } from '@core/utils/class.utils';

declare let tarteaucitronForceLanguage;
declare let tarteaucitronCustomText;
declare let tarteaucitron;
declare let tarteaucitronForceExpire;
declare let tarteaucitronExpireInDay;

/**
 * Cookie manager class.
 * Wrapper on tarteaucitron cookie manager.
 */
export default class StratisCookies {
    // Default options
    private readonly options: TarteaucitronSettingsModel = {
        // Cookies name.
        cookieName: 'tartaucitron',
        // Automatic opening of the panel with the hashtag.
        hashtag: '#tarteaucitron',
        // Disable implied consent (while browsing)?
        highPrivacy: true,
        // Banner position in html code.
        bodyPosition: 'top',
        // the privacy URL
        privacyUrl: '#',
        // Banner visual position.
        orientation: 'bottom',
        // Display a message if an adblocker is detected
        adblocker: false,
        // Display the small banner at the bottom right?
        showAlertSmall: false,
        // Display the list of installed cookies?
        cookieslist: false,
        // Remove the link to the source?
        removeCredit: true,
        handleBrowserDNTRequest: false,
        // Show accept all button
        AcceptAllCta: true,
        // add more info link
        moreInfoLink: true,
        // Show deny all button
        DenyAllCta: true,
        // If false, the tarteaucitron.css file will be loaded
        useExternalCss: true,
        // Show cookie manager icon
        showIcon: false,
        // Position of the icon between BottomRight, BottomLeft, TopRight and TopLeft
        iconPosition: 'BottomRight',
        // Domain name on which the cookie will be placed for subdomains
        // cookieDomain: '.example.com'
        // Display a fixed icon with a link to open the cookies module.
        closePopup: false,
        // In each services category, add a click & roll button to show/hide services.
        groupServices: false,
        // Declare an additional custom button used to open/close the cookies module.
        customCloserId: 'cookies-popup-btn',
    };

    private readonly language: string;
    private readonly customText: { [key: string]: string } | null;
    private readonly expireTime: number;
    private readonly expireInDays: boolean;
    private readonly reloadThePage: boolean;
    private readonly customServices: any[];

    /**
     * Add reload handler for service generated allow buttons.
     */
    static serviceAllowButtonsHandler(): void {
        const tacRoot = document.getElementById('tarteaucitronRoot') as HTMLElement;
        const allowButtons = [...document.querySelectorAll<HTMLButtonElement>('.gallery-item .tarteaucitronAllow:not(.has-listener)')]
            .filter(item => item && !tacRoot.contains(item));

        allowButtons.forEach(button => {
            button.classList.add('has-listener');

            button.addEventListener('click', e => {
                e.preventDefault();
                location.reload();
            });
        });
    }

    /**
     * Create options for 'tarteaucitron' cookies manager
     * @param options - object with plugin settings https://opt-out.ferank.eu/en/install/
     */
    public constructor(options: Partial<StratisCookiesModel> = {}) {
        this.language = options.language || 'fr';
        this.customText = options.customText || null;
        this.expireTime = options.expireTime || 180;
        this.expireInDays = options.expireInDays || true;
        this.reloadThePage = options.reloadThePage || false;

        this.customServices = [
            FancyboxCookie,
            YoutubeCookie,
            VimeoCookie,
            DailymotionCookie,
        ];

        if (options instanceof Object && options.config) {
            this.options = this.extendDefaults(this.options, options.config || {});
        }

        // Run initialization.
        this.init();
    }

    /**
     * Add services to cookies list
     * @param services - {String} services list
     */
    public addServices(services): void {
        const servicesArray = services.split(',');

        servicesArray.forEach(service => {
            (tarteaucitron.job = tarteaucitron.job || []).push(service.trim());
        });

        if (tarteaucitron.job.length) {
            tarteaucitron.job.forEach(serviceName => {
                document.addEventListener(`${serviceName}_added`, StratisCookies.serviceAllowButtonsHandler);
            });
        }
    }

    /**
     * Merge objects to 1
     * @param objects
     * @return {*}
     */
    private extendDefaults(...objects: object[]): any {
        const isObject = (obj): boolean => obj && typeof obj === 'object';

        return objects.reduce((prev, obj) => {
            Object.keys(obj)
                .forEach(key => {
                    const previousValue = prev[key];
                    const objectValue = obj[key];

                    if (Array.isArray(previousValue) && Array.isArray(objectValue)) {
                        prev[key] = previousValue.concat(...objectValue);
                    } else if (isObject(previousValue) && isObject(objectValue)) {
                        prev[key] = this.extendDefaults(previousValue, objectValue);
                    } else {
                        prev[key] = objectValue;
                    }
                });

            return prev;
        }, {});
    }

    /**
     * Add/remove page reload handlers for CTA buttons.
     */
    private addReloadHandlers(): void {

        tarteaucitron.reloadThePage = this.reloadThePage;

        window.addEventListener('tac.root_available', () => {
            setTimeout(() => {
                const buttons = [...document.querySelectorAll('#tarteaucitronRoot button')] as HTMLElement[];

                buttons.forEach(btn => {
                    btn.addEventListener('keyup', e => {
                        if (e.code === 'Space') {
                            e.preventDefault();
                            btn.click();
                        }
                    });

                    if (hasClass(btn, 'tarteaucitronDeny')) {
                        btn.addEventListener('click', () => {
                            if (location.hash) {
                                location.hash = '';
                                history.replaceState('', '', location.pathname); /* remove # */
                            }
                        });
                    }
                });
            }, 100);

            /**
             * This code blocks tarteaucitron issue when page don't reloaded after denied cookies
             */
            window.addEventListener('keydown', e => {
                if (e.keyCode === 27) {
                    e.preventDefault(); // not a mistake
                }
            });
        });
    }

    /**
     * Load plugin settings.
     */
    private loadSettings(): void {
        tarteaucitron.loadListeners = [];

        if (tarteaucitronForceLanguage !== undefined && this.language) {
            tarteaucitronForceLanguage = this.language;
        }

        if (tarteaucitronCustomText !== undefined && typeof this.customText === 'object') {
            tarteaucitronCustomText = this.customText;
        }

        if (tarteaucitronForceExpire !== undefined && this.expireTime) {
            tarteaucitronForceExpire = this.expireTime;
        }

        if (tarteaucitronExpireInDay !== undefined && typeof this.expireInDays === 'boolean') {
            tarteaucitronExpireInDay = this.expireInDays;
        }

        this.addReloadHandlers();
    }

    /**
     * Load cookie plugin custom services.
     */
    private loadCustomServices(): void {
        this.customServices.forEach((ServiceConstructor: Constructor) => {
            try {
                const _ = new ServiceConstructor();
            } catch (err: any) {
                console.error(`${err.message}. Cookie service "${ServiceConstructor}" unregistered`);
            }
        });
    }

    /**
     * Handle all listeners on ttc load event.
     */
    private loadEventListeners(): void {
        // Run listeners for load event.
        tarteaucitron.events.load = () => {
            if (tarteaucitron.loadListeners.length) {
                tarteaucitron.loadListeners.forEach(listener => {
                    listener();
                });
            }
        };
    }

    /**
     * Initialise 'tarteaucitron' cookies manager
     */
    private init(): void {
        try {
            this.loadSettings();
            this.loadCustomServices();
            this.loadEventListeners();

            // Init tarteaucitron plugin.
            tarteaucitron.init(this.options);
        } catch (error) {
            console.error(error);
        }
    }
}
