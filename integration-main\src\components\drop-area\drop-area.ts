import StratisFactoryMixin from '@core/mixins/stratis-factory.mixin';
import { IGlobalOptions } from '@core/interfaces/stratis-element.interface';
import { FileListBlock } from '@core/core-components/file-list';
import { EventsBindSchema } from '@core/interfaces/base-types';
import FileInputHandler from '@core/abstract/file-input.abstract';
import { throttle } from '@core/utils/function.utils';
import { addClass, removeClass } from '@core/utils/class.utils';

export class DropArea extends FileInputHandler {
    protected options: IGlobalOptions<DropArea> = {
        classList: {
            highlighter: 'is-highlighted',
        },
        DOMElements: {
            input: 'input[type="file"]',
            area: '.js-drop-area-block',
            filesList: '.js-files-list',
        },
        dataset: {},
    };

    protected fileListBlock: FileListBlock | null = null;

    // Bindings
    protected readonly $enterHandler = this.enterHandler.bind(this);
    private static dropAreaElements: HTMLElement[] = [];

    public constructor(element: HTMLElement, options: IGlobalOptions<DropArea>) {
        super(element, options || {});

        if (!this.created) {
            this.init();
        }
    }

    /**
     * Handle page drop area highlight
     * @param selector - area blocks selectors
     * @param globalTargetClass - global highlighter class
     */
    public static handlePageFileDrop(selector = '.js-drop-area-block', globalTargetClass = 'is-target'): void {
        this.dropAreaElements = [...document.querySelectorAll<HTMLElement>(selector)];

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, e => throttle.call(this, DropArea.handleFilePageDrop(e, globalTargetClass), 200));
        });
    }

    /**
     * Create events and set handles for it
     */
    protected createEvents(events: EventsBindSchema): void {
        const { input, area } = this.options.DOMElements as { input: HTMLInputElement; area: HTMLElement };

        super.createEvents(events || [
            [area, ['dragenter', 'dragover', 'dragleave', 'drop'], this.toggleHighlightArea],
            [area, 'drop', this.handleDrop],
            [area, 'click', this.handleAreaClick],
            [input, 'change', this.handleInputChange],
        ]);
    }

    /**
     * Open file input on area block click
     * @param e - Event
     */
    protected handleAreaClick(e: Event): void {
        const { input } = this.options.DOMElements as { input: HTMLInputElement };

        e.preventDefault();
        input.click();
    }

    /**
     * Highlight all drag and drop area blocks
     * @param e - Event
     * @param globalTargetClass - highlighter class
     */
    private static handleFilePageDrop(e: Event, globalTargetClass: string): void {
        const state = e.type === 'dragenter' || e.type === 'dragover';

        e.preventDefault();
        e.stopPropagation();
        DropArea.dropAreaElements.forEach(element => {
            if (state) {
                addClass(element, globalTargetClass);
            } else {
                removeClass(element, globalTargetClass);
            }
        });
    }
}

const DropAreaFactory = StratisFactoryMixin<typeof DropArea, DropArea, any>(DropArea);
export default DropAreaFactory;
