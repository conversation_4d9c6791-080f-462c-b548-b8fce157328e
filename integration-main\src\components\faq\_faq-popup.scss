.faq-popup {
    &__wrapper {
        @include size(1200px, 650px);
        background-color: $color-white;
        column-gap: 25px;
        display: flex;
        flex-direction: row;
        padding: 66px 28px 33px 53px;
        position: relative;

        &.is-comments {
            @include size(1200px, auto);
            flex-direction: column;

            @include breakpoint(medium down) {
                @include size(100%, auto);
            }
        }

        @include breakpoint(medium down) {
            @include size(100%, auto);
            column-gap: 20px;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            padding: 64px 20px 20px;
        }
    }

    &__content {
        flex-grow: 1;
        overflow-y: auto;
        padding-top: 32px;
    }

    &__content-wrap {
        display: flex;
        flex-direction: column;
    }

    &__title {
        margin-bottom: 29px;

        &-text {
            @include font(var(--typo-1), 2.6rem, var(--fw-bold));
            color: $color-black;
            line-height: 1.2;

            @include breakpoint(medium down) {
                font-size: 3.2rem;
            }

            @include breakpoint(small down) {
                font-size: 2.8rem;
                line-height: 1.2;
            }
        }
    }

    .popup__close-btn {
        @include absolute(10px, 10px, null, null);
        border: 1px solid var(--color-1--1) !important;

        span[class*="fa-"] {
            &::before {
                color: var(--color-1--1);
            }
        }
    }
}
