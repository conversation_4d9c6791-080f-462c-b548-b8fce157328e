{%- from 'views/utils/utils.njk' import svg -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'views/core-components/section.njk' import Section -%}

{#
    CommentsItem template.
#}
{%- macro CommentsItem(replyLink = true, adminAvatar = false) -%}
    <div class="comment-item">
        <div class="comment-item__avatar">
            {% if not adminAvatar %}
                {{ svg('icons/avatar' + range(1, 9) | random, 42, 42) }}
            {% else %}
                {{ svg('icons/avatar-admin', 42, 42) }}
            {% endif %}
        </div>
        <div class="comment-item__container">
            <div class="comment-item__wrapper">
                <p class="comment-item__header">
                    <span class="comment-item__author">Pseudo001</span>
                    <span class="comment-item__date"> - Posté le <time datetime="2016-07-10 18:02">01/03/2021</time></span>
                </p>
                <div class="comment-item__content">
                    <p>
                        {{ lorem(2) }}
                        {{ lorem(5, 'words') | capitalize }} <a href="#">eius eligendi hic impedit</a> {{ lorem(3, 'words') }}
                        {{ lorem(2) }}
                    </p>
                </div>
                {% if replyLink %}
                    <div class="comment-item__reply-link">
                        <p><a href="#comment-reply" title="Répondre à [PSEUDO OF CURRENT COMMENT]">Répondre</a></p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{%- endmacro -%}

{#
    CommentsForm template.
#}
{%- macro CommentsForm() -%}
    {% call Form.FormWrapper(legend = false) %}
        <fieldset class="form__fieldset">
            <legend class="form__legend has-mb-4">Ajouter un commentaire</legend>
            {% call Form.FormAvatarsGroup() %}
                {% for avatarIndex in range(1, 10) %}
                    {{ Form.FormAvatar('avatar' + avatarIndex) }}
                {% endfor %}
            {% endcall %}
            {% call Form.FormGroup() %}
                <div class="col-lg-6 col-xs-12">
                    {% call Form.FormField(
                        type = 'email',
                        label = 'Courriel',
                        required = true,
                        placeholder = false,
                        customAttrs = {
                        'autocomplete' : 'email',
                        'aria-describedby' : 'email-not-displayed'
                    }
                        ) %}
                        <span class="text-help" id="email-not-displayed" aria-hidden="true">Ex : <EMAIL> (Ne sera pas affiché sur le site)</span>
                    {% endcall %}
                </div>
                <div class="col-lg-6 col-xs-12">
                    {% call Form.FormField(
                            type = 'text',
                            label = 'Pseudonyme',
                            required = true,
                            placeholder = false,
                            customPlaceholder = false,
                            customAttrs = {
                                'autocomplete' : 'nickname',
                                'aria-describedby' : 'text-help'
                            }
                        ) %}
                        <span class="text-help is-tooltip" id="text-help" aria-hidden="true">Pour des raisons de confidentialité, évitez de saisir un pseudonyme permettant de vous identifier</span>
                    {% endcall %}
                </div>
                <div class="col-lg-12 col-xs-12">
                    {% call Form.FieldGroup(
                        type = 'textarea',
                        label = 'Ajouter un commentaire',
                        placeholder = false,
                        required = true
                    ) %}
                        {{ Form.RadioCheckbox(
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données.
                            <a href="javascript:;"
                                role="button"
                                aria-haspopup="dialog"
                                data-dialog-label="Page de politique de protection des données"
                                data-type="iframe"
                                data-fancybox
                                data-src="./test-form.html"
                                data-title="Page de politique de protection des données">
                                Cliquez ici pour les consulter
                            </a>',
                            required = true,
                            disableRequiredLabel = false
                        ) }}
                    {% endcall %}
                </div>
            {% endcall %}
        </fieldset>
        {% call Form.FormActions(className = 'is-lg-right is-wrap is-middle') %}
            {{ Button(
                className = 'btn is-basic is-small has-mr-2',
                icon = false,
                type = 'reset',
                attrs = {
                    'title' : 'Effacer les champs précédemment saisis'
                },
                text = 'Effacer'
            ) }}
            {{ Button(
                className = 'btn is-primary',
                icon = 'far fa-paper-plane',
                type = 'submit',
                attrs = {
                    'title' : 'Envoyer mon commentaire'
                },
                text = 'Envoyer mon commentaire'
            ) }}
        {% endcall %}
    {% endcall %}
{%- endmacro -%}

{#
    Comments template.
#}
{%- macro Comments() -%}
    <div class="comments">
        <div class="comments__content">
            <ul class="comments__listitems">
                {%- for item in range(2) %}
                    <li class="comments__item"  id="{{ Helpers.unique() }}">
                        {{ CommentsItem() }}

                        {% if loop.index === 2 %}
                            <ul class="comments__listitems is-lvl-2">
                                <li class="comments__item"  id="{{ Helpers.unique() }}">
                                    {{ CommentsItem(replyLink = false, adminAvatar = true) }}
                                </li>
                            </ul>
                        {% endif %}
                    </li>
                {%- endfor %}
            </ul>
        </div>
        <div class="comments__form" id="comment-reply">
            {{ CommentsForm() }}
        </div>
    </div>
{%- endmacro -%}

{#
    NewsContent template.
#}
{%- macro CommentsContent() -%}
    {% call Section(className = 'comments-content', container = false) %}
        <div class="section__title">
            {{ Title.TitleRTE(
                text = 'Commentaires'
            ) }}
        </div>
        <div class="section__content">
            {{ Comments() }}
        </div>
    {% endcall %}
{%- endmacro -%}


{#
    CommentsSG template.
    Comments template for styleguide.
#}
{%- macro CommentsSG() -%}
    {% call SG.Section('comments') %}
        <h2 class="styleguide-section__title">Comments</h2>
        {%- call SG.Preview() -%}
            <div class="flex-row">
                <div class="col-sm-12">
                    <div class="has-mb-3">
                        <div class="comments__content">
                            <ul class="comments__listitems">
                                {%- for item in range(4) %}
                                    <li class="comments__item" id="{{ Helpers.unique() }}">
                                        {{ CommentsItem() }}

                                        {% if loop.index === 1 %}
                                            <ul class="comments__listitems is-lvl-2">
                                                <li class="comments__item" id="{{ Helpers.unique() }}">
                                                    {{ CommentsItem() }}
                                                </li>
                                            </ul>
                                        {% endif %}
                                    </li>
                                {%- endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        {%- endcall -%}
    {%- endcall -%}
{%- endmacro -%}

