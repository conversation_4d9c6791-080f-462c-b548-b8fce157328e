export interface TarteaucitronSettingsModel {
    cookieName: string;
    hashtag: string;
    highPrivacy: boolean;
    bodyPosition: 'top' | 'bottom';
    privacyUrl: string;
    orientation: 'top' | 'bottom' | 'middle';
    adblocker: boolean;
    showAlertSmall: boolean;
    cookieslist: boolean;
    removeCredit: boolean;
    handleBrowserDNTRequest: boolean;
    AcceptAllCta: boolean;
    moreInfoLink: boolean;
    DenyAllCta: boolean;
    useExternalCss: boolean;
    showIcon: boolean;
    iconPosition: 'BottomRight' | 'BottomLeft' | 'TopRight' | 'TopLeft';
    cookieDomain?: string;
    closePopup: boolean;
    groupServices: boolean;
    customCloserId: string;
}

export interface StratisCookiesModel {
    config: Partial<TarteaucitronSettingsModel>;
    language: string;
    expireTime: number;
    expireInDays: boolean;
    reloadThePage: boolean;
    customText: {
        [key: string]: string;
    };
}
