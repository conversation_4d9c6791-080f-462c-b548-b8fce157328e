// Filter component.
.filter {
    $this: &;

    .map-page & {
        display: flex;
        flex-direction: column;
    }

    .form {
        &__legend {
            margin: 0 0 40px;
            padding-right: 30px;
    
            @include breakpoint(small down) {
                margin: 0 0 65px;
            }
    
            .map-page & {
                margin-bottom: 20px;
            }
        }

        &__label {
            @include breakpoint(large only) {
                width: 110px;

                .is-list-job & {
                    width: 195px;
                }

                .is-organigramme & {
                    flex-shrink: 0;
                    width: fit-content;
                }

                .is-list-with-filter & {
                    width: 100%;
                }
            }
        }

        &__label[class],
        .multiselect-wrapper__legend {
            margin: 0 20px 0 0;

            body.is-search & {
                flex-shrink: 0;
                width: fit-content;
            }
        }

        &__field-wrapper,
        .multiselect-wrapper {
            margin-bottom: 0;
            width: 100%;

            @include breakpoint(large only) {
                align-items: center;
                display: flex;
            }

            body:not(.is-list-with-filter) & {
                max-width: 100%;
                width: auto;
            }

            .search-filter-event & {
                display: block;
            }
        }

        .multiselect__toggle {
            border: 0;
            color: $color-black;
        }

        &__field,
        .multiselect__toggle {
            background-color: transparent;
            min-height: 44px;
            padding: 0;

            body.is-search &,
            .is-organigramme & {
                width: 100%;
            }
        }
    }
    

    #{$this}:not(.popup &):not(.map-page &) {
        #{$this}__wrapper-inner {
            @extend %container;
            padding: 0;
        }
    }

    &__fields {
        width: 100%;
    }

    &__nav {
        .map-page & {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            height: 1%;
        }
    }

    &__buttons {
        display: flex;
        justify-content: center;
        margin: 15px 0 0;

        @include breakpoint(medium down) {
            margin-top: 0;
        }
    }

    &__fields-group {
        @extend %grid-row;
        margin-left: 0;
        margin-right: 0;
        max-width: 995px;
        width: 100%;

        .is-single-forum &,
        .is-list-forum-elements &,
        .is-list-job &,
        .is-list-ideas-box &,
        .is-organigramme & {
            max-width: 100%;
        }

        .radio-checkbox__label {
            @include font(var(--typo-1), 1.8rem !important, var(--fw-normal) !important);

            @include breakpoint(medium down) {
                font-size: 1.6rem !important;
            }
        }

        .multiselect {
            width: 100%;
        }

        select,
        .multiselect,
        input {
            @include font(var(--typo-1), 1.8rem, var(--fw-normal));
            @include size(282px,52px);
            background-color: transparent;
            border: 0;
            border-bottom: 1px solid $color-3--4;
            color: $color-black;
            padding: 0;

            @include breakpoint(small down) {
                font-size: 1.2rem;
            }

            .is-list-ideas-box & {

                @include breakpoint(medium down) {
                    min-height: 18px;
                }
            }

            @include breakpoint(medium down) {
                @include size(100%, 35px);
            }

            &::after {
                color: $color-black !important;
            }

            &::placeholder {
                color: $color-black;
            }

            &:focus-visible {
                border-bottom: 1px solid $color-black;
                outline: none;
            }
        }

        .multiselect__toggle,
        select {
            background: {
                image: var(--global-select-caret-white);
                position:  top 54% right 0;
                repeat: no-repeat;
            };
        }

        .pager-alphabet__link {
            border: solid 1px var(-color-1--1);
            color: var(-color-1--1);

            @include on-event {
                background-color: var(--color-2--1);
                border-color: $color-black;
                color: $color-black;
            }

            &.is-current {
                background-color: var(--color-2--1);
                border-color: var(--color-2--1);
                color: $color-black;
                font-weight: var(--fw-normal);
            }
        }
    }

    &__field {
        @include max-size(435px, 63px);
        align-items: center;
        display: flex;
        margin-bottom: 12px;
        padding-left: 12px;
        padding-right: 12px;
        width: auto;

        @include breakpoint(medium down) {
            margin-bottom: 38px;
        }

        @include breakpoint(small down) {
            width: 100%;
        }

        > * {
            margin-bottom: 25px;

            &:not(:first-child).radio-checkbox {
                margin-top: -10px;
            }
        }

        .is-list-with-filter & {
            max-height: 80px;

            @include breakpoint(small down) {
                max-height: 74px;
            }
        }
    }

    &__fields-dropdown-toggle {
        @include trs;
        @include font(var(--typo-1), 1.4rem, var(--fw-bold));
        background: none;
        border: 0;
        color: $color-black;
        cursor: pointer;
        display: inline-block;
        margin-bottom: 20px;
        padding: 0;
        text-align: left;
        text-decoration: underline;

        @include on-event {
            color: var(--color-1--1);
        }
    }

    &__fields-dropdown-block {
        display: none;

        &.is-open {
            display: block;
        }
    }
}

.filter-submit {
    @extend %button;
    @extend %button-style-primary;

    font-size: 1.2rem;
    height: 1px; // For IE
    min-height: 43px;
    padding: 14px 33px;

    @include fa-icon-style(false) {
        display: table-cell; // For IE
        vertical-align: middle; // For IE
    }
}

.filter-reset {
    @extend %button;
    @include font(var(--typo-1), 1.3rem, var(--fw-bold));
    background-color: var(--color-1--1);
    border: 1px solid var(--color-2--1);
    color: var(--color-2--1);
    text-decoration: none;
    text-transform: uppercase;
    @include size(209px, 60px);

    @include breakpoint(medium down) {
        font-size: 1.2rem;
        @include size(100%,45px);
    }

    @include on-event {
        background-color: transparent;
        color: var(--color-1--1);
        text-decoration: none;
    }
}

.filter-candidacy {
    @include font(var(--typo-1) , 1.3rem , var(--fw-bold));
    background-color: var(--color-2--1);
    border: 1px solid var(--color-2--1);
    color: $color-black;
    font-size: 1.3rem;
    height: 60px;
    text-transform: uppercase;

    @include breakpoint(small down) {
        font-size: 1.2rem;
        @include size(100%,45px);
    }
}

.filter-wrap {
    @extend %container;
    align-items: center;
    display: flex;
    margin: 0 auto 45px;

    @include breakpoint(medium down) {
        align-items: flex-start;
        flex-direction: column;
    }

    &__buttons {
        display: flex;
        flex-shrink: 0;
        flex-wrap: wrap;
        margin-left: -5px;

        @include breakpoint(medium down) {
            margin-bottom: 25px;
            width: 100%;
        }

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: column;
        }

        > * {
            margin: 5px !important;
        }
    }
}

.filter-wrapper {
    @extend %container;
    background-color: $color-3--1;
    margin-bottom: 20px;
    max-width: 1200px;
    padding: 23px 67px 51px;

    @include breakpoint(medium down) {
        padding: 37px 20px 32px 20px;
    }

    @include breakpoint(small down) {
        margin: -3px 0 0;
        padding: 62px 12px 23px;
        width: 100%;

        body.is-search & {
            @include breakpoint(small down) {
                padding-top: 28px;
            }
        }
    }

    @include breakpoint(large only) {
        padding-inline: 28px;
    }

    .has-page-image & {
        @include breakpoint(large only) {
            margin-bottom: 39px;
        }
    }

    .is-organigramme & {
        @include breakpoint(large only) {
            padding: 23px 81px 32px 81px;
        }

        @include breakpoint(small down) {
            padding-bottom: 30px;
        }
    }

    .is-list-forum &,
    .is-list-faq &,
    .is-list-ideas-box &,
    .is-list-forum-elements &,
    .is-list-job &,
    .is-list-news & {
        margin-bottom: 39px;
        max-width: 1200px;
    }

    input[type="date"]::-webkit-calendar-picker-indicator {
        cursor: pointer;
        opacity: 0;
        position: relative;
        z-index: 2;
    }

    .js-autocomplete-result-wrapper {
        left: inherit;
    }

    .js-autocomplete.is-visible {
        background-color: $color-3--1;
    }

    .js-autocomplete-input-clear {
        @include size(30px !important);
        border: 1px solid $color-black !important;
        top: 20px;
    }
}

.without-icon {
    position: relative;

    &::-webkit-calendar-picker-indicator {
        cursor: pointer;
        opacity: 0;
    }
}
