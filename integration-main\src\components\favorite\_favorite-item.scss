.favorite-item {
    $this: &;

    @extend %link-block-context;
    @extend %underline-context;
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    width: 100%;
    z-index: 2;

    @include breakpoint(medium down) {
        align-items: flex-start;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }

    &__image {
        order: -1;
        position: relative;
        z-index: -1;

        @include breakpoint(medium down) {
            flex-shrink: 0;
            width: 229px;
        }

        @include breakpoint(small down) {
            width: 100px;
        }
    }

    &__content {
        background-color: $color-white;
        margin-top: -61px;
        max-width: 282px;
        padding: 35px 35px 0 0;

        @include breakpoint(medium down) {
            margin: 0;
            max-width: calc(100% - 229px);
            padding: 0 0 0 37px;
        }

        @include breakpoint(small down) {
            max-width: calc(100% - 100px);
            padding: 0 0 0 10px;
        }
    }

    &__category {
        margin: 0 0 13px;
    }

    &__title {
        margin: 0 0 20px;
    }

    &__date {
        @include font(null, 1.2rem, var(--fw-normal));
        color: $color-3--4;
        margin: 0 0 20px;
        padding-left: 45px;
        position: relative;
        text-transform: uppercase;

        &::before {
            @include absolute(6px, null, null, 0);
            @include size(35px, 4px);
            background-color: var(--color-2--1);
            content: '';
        }
    }

    &__remove {
        @extend %button;
        @extend %button-size-small;
        z-index: 4;

        @include breakpoint(medium down) {
            margin-left: 266px;
        }

        @include breakpoint(small down) {
            margin-left: 110px;
        }
    }
}
