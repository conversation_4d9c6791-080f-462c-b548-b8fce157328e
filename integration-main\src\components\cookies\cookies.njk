{% from 'views/utils/utils.njk' import getScriptPath %}

{#
    Cookies component
#}
{%- macro CookiesScripts() -%}
    {# Load tarteaucitron main lib #}
    <script src="{{ getScriptPath('tarteaucitron') }}" defer></script>
    {#
        This script will be connected from backend.
    #}
    <script>
        /**
         * Backend devs should provide this configuration.
         * This object contains all cookie plugin settings and custom texts and also some custom logic for frontend.
         */
        var stratisCookieOptions = {
            // Cookie language, it will be used for loading translations.
            language: 'fr',
            // Cookie expire time.
            expireTime: 180,
            // Cookie expire time type, can be days/hours
            expireInDays: true,
            // Reload the page if we accept or deny cookie.
            reloadThePage: false,
            // Cookie custom texts.
            customText: {
                alertBigClick: '',
                alertBig: 'Ce site utilise des cookies pour favoriser votre navigation et enrichir les contenus qui vous sont proposés. <br />Vous pouvez néanmoins les désactiver à tout moment si vous le souhaitez.<br /><br />',
                alertBigPrivacy: 'Afin de vous proposer des vidéos, des boutons de partage, des contenus remontés des réseaux sociaux et d\'élaborer des statistiques de fréquentation, nous sommes susceptibles de déposer des cookies tiers sur votre machine. Cela ne peut se faire qu\'en obtenant, au préalable, votre consentement pour chacun de ces cookies.',
                disclaimer: '<p>Ce site propose de personnaliser vos contenus et votre navigation. Lorsque vous naviguez sur ce site Internet, des informations sont susceptibles d\'être enregistrées (cookies) sur votre terminal, sous réserve de vos choix.</p>'+
                '<p>La durée de validité de votre consentement ou de votre refus est de 6 mois.<br>Pour en savoir plus, consultez notre <a href="#">politique de protection des données.</a></p>',
                denyAll: 'Tout refuser',
                more: 'Détail des cookies',
                btnAllow: 'J\'accepte',
                btnAllowTitle: 'J\'accepte les traceurs pour le service {serviceName} (recharger la page)',
                btnDeny: 'Je refuse',
                btnDenyTitle: 'Je refuse les traceurs pour le service {serviceName}',
                refuseCookies: 'Votre refus a bien été pris en compte. Vous pouvez revenir sur votre décision à tout moment, depuis le gestionnaire de cookies ou cet écran.',
                serviceText: '{serviceName} conditionne la lecture de ses vidéos au dépôt de traceurs afin de vous proposer de la publicité ciblée en fonction de votre navigation.',

                // Cookie custom services texts.
                'engage-youtube': 'Activer le dépôt de cookies Youtube pour lire la vidéo',
                'engage-vimeo': 'Activer le dépôt de cookies Vimeo pour lire la vidéo',
                'engage-dailymotion': 'Activer le dépôt de cookies Dailymotion pour lire la vidéo',
                'engage-stratis-youtube': 'Activer le dépôt de cookies Youtube pour lire la vidéo',
                'engage-stratis-vimeo': 'Activer le dépôt de cookies Vimeo pour lire la vidéo',
                'engage-stratis-dailymotion': 'Activer le dépôt de cookies Dailymotion pour lire la vidéo',
                'open-cookie-manager-button': 'Configurer le dépôt de cookies'
            },

            // Tarteaucitron plugin configuration
            config: {
                // Cookies name.
                cookieName: 'tartaucitron',
                // Automatic opening of the panel with the hashtag.
                hashtag: '#tarteaucitron',
                // Disable implied consent (while browsing)?
                highPrivacy: true,
                // Banner position in html code.
                bodyPosition: 'top',
                // the privacy URL
                privacyUrl: '#',
                // Banner visual position.
                orientation: 'bottom',
                // Display a message if an adblocker is detected
                adblocker: false,
                // Display the small banner at the bottom right?
                showAlertSmall: false,
                // Change tarteaucitronTitle button into text
                showDetailsOnClick:false,
                // Display the list of installed cookies?
                cookieslist: false,
                // Remove the link to the source?
                removeCredit: true,
                handleBrowserDNTRequest: false,
                // Show accept all button
                AcceptAllCta: true,
                // add more info link
                moreInfoLink: true,
                // Show deny all button
                DenyAllCta: true,
                // If false, the tarteaucitron.css file will be loaded
                useExternalCss: true,
                // Show cookie manager icon
                showIcon: false,
                // Position of the icon between BottomRight, BottomLeft, TopRight and TopLeft
                iconPosition: 'BottomRight',
                // Domain name on which the cookie will be placed for subdomains
                // cookieDomain: '.example.com'
            },
        };
    </script>
    <script src="{{ getScriptPath('stratis-tarteaucitron.bundle') }}" defer></script>
    {#
        Load tarteaucitron services from backend.
    #}
    <script>
        window.addEventListener('tac.root_available', function() {
            /**
             * Add services to cookies manager
             * This code is used for frontend and should be replaced by backend
             *
             *
             * This line:
             * (tarteaucitron.job = tarteaucitron.job || []).push('youtube');
             *
             * should be replaced with:
             * window.cookieManager.addServices('youtube')
             */
            if (window.cookieManager) {
                window.cookieManager.addServices('stratis-youtube, stratis-vimeo, stratis-dailymotion, fancybox');
            }
        });
    </script>
{%- endmacro -%}
