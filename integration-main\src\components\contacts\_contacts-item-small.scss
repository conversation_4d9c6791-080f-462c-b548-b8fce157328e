// .contact-item-small {
//     display: flex;
//     align-items: center;
//     background-color: #ffffff;
//     border-radius: 8px;
//     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
//     padding: 10px;
//     margin: 10px 0;
//     max-width: 350px;
//     width: 100%;
    
//     &__picture {
//         width: 60px;
//         height: 60px;
//         border-radius: 50%;
//         object-fit: cover;
//         margin-right: 15px;
//     }
    
//     &__content {
//         display: flex;
//         flex-direction: column;
//         justify-content: center;
//     }
    
//     &__name {
//         font-size: 1.1rem;
//         font-weight: bold;
//         color: #333;
//         margin: 0;
//     }
    
//     &__function {
//         font-size: 0.9rem;
//         color: #666;
//         margin: 5px 0 0;
//     }
    
//     // Optional: Add a hover effect for interactivity
//     &:hover {
//         background-color: #f5f5f5;
//         cursor: pointer;
//         box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
//     }
// }
