.comment-item {
    --com-avatar-size: 66px;
    $this: &;

    &__avatar {
        @include size(var(--com-avatar-size));
        align-items: center;
        background-color: var(--color-1--1);
        border-radius: 50%;
        display: flex;
        float: left;
        justify-content: center;
        margin-right: 10px;

        @include breakpoint(small down) {
            @include size(55px);
            margin-right: 5px;
        }

        svg {
            @include size(42px);
            fill: $color-white;

            @include breakpoint(small down) {
                @include size(32px);
            }
        }

        .is-lvl-2 & {
            background-color: var(--color-1--3);

            svg {
                fill: var(--color-1--2);
            }
        }
    }

    &__avatar-arras {
        @include size(var(--com-avatar-size));
        align-items: center;
        border: 1px solid var(--color-1--1);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        left: 3px;
        margin-right: 10px;
        position: relative;
        top: -24px;

        svg {
            @include size(66px,30px);
            object-fit: cover;
        }
    }

    &__arras-documents {
        margin-bottom: 18px;
        margin-left: 25px;

        .telecharger__list {
            margin: 0 !important;
        }

        .telecharger__list-item {
            margin-bottom: 15px;

            &::before {
                content: none !important;
            }
        }

        .telecharger-item {
            align-items: flex-start;
            background-color: var(--color-2--1);
            display: flex;
            gap: 148px;
            padding: 0;
            position: relative;
            width: 862px;

            @include breakpoint(medium down) {
                flex-direction: column;
                gap: 0;
                width: 100%;
            }

            &__svg-wrap {
                @include size(19px,25px);
                @include absolute(4px, null, null, -27px);

                svg {
                    @include size(100%);
                    fill: var(--color-1--1);
                }
            }

            &__text-arras-description {
                max-width: 992px;
            }

            &__meta {
                @include font(var(--typo-1), 1.4rem, var(--fw-normal));
                color: var(--color-1--1);
                display: block;
                margin: 0 5px;
                text-transform: uppercase;
            }

            &__title {
                @include font(var(--typo-1), 1.6rem !important, var(--fw-normal) !important);
                color: var(--color-1--1);
                width: 418px;

                @include breakpoint(medium down) {
                    width: 100%;
                }
            }

            &__descr {
                align-items: center;
                display: flex;
                justify-content: space-between;
                width: 265px;

                @include breakpoint(medium down) {
                    align-items: flex-start;
                    flex-direction: column;
                    width: 100%;
                }
            }

            &__link {
                @include font(var(--typo-1), 1.2rem, var(--fw-normal));
                color: var(--color-1--1);
                display: flex;
                gap: 4px;
                line-height: 1.4;
                text-decoration: none;

                @include on-event {
                    background-color: $color-white;
                    color: var(--color-1--1);
                }
            }

            &__file-group {
                @include font(var(--typo-1), 1.4rem, var(--fw-normal));
                align-items: center;
                color: $color-3--5;
            }
        }
    }

    &__container {
        overflow: hidden;
    }

    &__wrapper {
        padding: 26px 0 0 26px;
        position: relative;

        @include breakpoint(small down) {
            padding: 20px 0 0 5px;
        }
    }

    &__header {
        @include font(var(--typo-1), 1.2rem);
        color: $color-3--4;
        margin: 0;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.2rem;
        }
    }

    &__reply-link {
        margin-top: 20px;

        p {
            @include font(var(--typo-1), 1.2rem, var(--fw-bold));
            @include icon-before($fa-var-comment-alt);
            color: var(--color-1--2);
            margin: 0;
            text-transform: uppercase;

            a {
                color: var(--color-1--2);
                text-decoration: none;

                @include on-event {
                    text-decoration: underline;
                }
            }

            &::before {
                margin-right: 8px;
            }

            @include breakpoint(small down) {
                font-size: 1.2rem;
            }
        }
    }

    &__author {
        @include font(null, null, var(--fw-bold), normal);

        @include breakpoint(small down) {
            display: block;
        }
    }

    &__content {
        p {
            @include font(var(--typo-1), 1.6rem);
            color: $color-black;
            line-height: 1.5;
            margin: 10px 0 0;

            @include breakpoint(small down) {
                font-size: 1.4rem;
            }

            a {
                color: inherit;

                @include on-event {
                    text-decoration: none;
                }
            }
        }
    }
}
