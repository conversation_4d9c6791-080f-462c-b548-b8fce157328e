{%- from 'components/subscriptions/subscriptions.njk' import Subscriptions -%}
{%- from 'components/site-info/site-info.njk' import SiteInfo -%}
{%- from 'components/partners/partners.njk' import Partners -%}

{#
    Footer template.
    @param {boolean} - check if is home page.
#}
{%- macro Footer(
    isHome = false,
    isInverted = false,
    subfooter = true
) -%}
    <footer class="footer{{ ' is-inverted' if isInverted }}" role="contentinfo">
        {% if subfooter %}
            <div class="footer__subfooter">
                {{ Subscriptions(listItems = [[['sms'], ['newsletter']]]) }}
            </div>
        {% endif %}
        <div class="footer__wrapper">
            <div class="footer__container">
                <div class="footer__row">
                    <div class="footer__column is-first">
                        {{ SiteInfo(isHome) }}
                    </div>
                </div>
            </div>
        </div>
    </footer>
{%- endmacro -%}
