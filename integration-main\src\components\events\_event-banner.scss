.event-banner {
    $this: &;

    @extend %link-block-context;
    align-items: stretch;
    display: flex;
    flex-direction: row-reverse;
    position: relative;
    z-index: 1;

    @include breakpoint(medium only) {
        margin: 0 22px;
    }

    @include breakpoint(small down) {
        flex-direction: column-reverse;
    }

    &__image {
        width: 50%;

        @include breakpoint(small down) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__content {
        @include size(50%, 100%);
        padding: 40px 100px 40px 65px;

        @include breakpoint(medium down) {
            padding: 21px 60px 21px 30px;
        }

        @include breakpoint(small down) {
            padding: 28px 50px 28px 34px;
            width: 100%;
        }
    }

    @include fa-icon-style(false) {
        @include trs;
        @include absolute(null, 60px, 52px, null);
        font-size: 3rem;
        font-weight: var(--fw-normal);

        @include breakpoint(medium down) {
            @include absolute(null, 32px, 28px, null);
            font-size: 1.6rem;
        }

        @include breakpoint(small down) {
            @include absolute(null, 24px, 30px, null);
        }
    }

    &__title {
        display: inline;
        font-size: 4.5rem;

        @include breakpoint(medium down) {
            font-size: 2.4rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus-visible {
            &::after {
                outline: 3px solid currentColor !important;
                outline-offset: -4px;
            }
        }
    }
}
