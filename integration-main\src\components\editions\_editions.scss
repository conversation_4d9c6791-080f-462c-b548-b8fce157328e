.editions {
    $this: &;

    &.section {
        margin: 70px 0 78px;

        @include breakpoint(large only) {
            margin: 70px 0 78px;
        }

        @include breakpoint(medium down) {
            margin-bottom: 83px;
        }

        @include breakpoint(small down) {
            margin-bottom: 70px;
        }
    }

    .section__more-links {
        margin-top: 0;
        padding-left: 305px;

        @include breakpoint(medium down) {
            justify-content: flex-start;
            padding-left: 247px;
        }

        @include breakpoint(small down) {
            justify-content: center;
            padding-left: 0;
        }
    }

    .section {
        &__title {
            @include breakpoint(large only) {
                margin-left: 606px;
                position: relative;
                top: 49px;
                z-index: 2;
            }

            @include breakpoint(medium down) {
                margin-bottom: 21px;
            }

            @include breakpoint(small down) {
                margin-bottom: 31px;
            }
        }
    }

    &__container {
        @extend %container;
        @extend %container-lg;
        position: relative;

        @include breakpoint(medium down) {
            padding: 0 40px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }

    &__item {
        direction: ltr;
        max-width: 281px;
        padding: 22px 0 12px;

        @include breakpoint(medium down) {
            max-width: 100%;
            padding: 20px;
        }

        @include breakpoint(small down) {
            padding: 0;
        }

        &.swiper-slide-active {
            pointer-events: auto;

            .edition-item {
                display: flex;

                @include breakpoint(small down) {
                    display: block;
                }

                &__image-wrapper {
                    @include breakpoint(large only) {
                        @include size(289px, 408px);
                        transform: rotate(-5.14deg);
                    }
                }
            }
        }

        &.swiper-slide-next {
            .edition-item {
                &__image-wrapper {
                    @include breakpoint(large only) {
                        @include size(278px, 376px);
                        transform: rotate(-1.14deg) translateY(18px);
                    }
                }
            }
        }
    }

    .swiper-container-coverflow .swiper-wrapper {
        /* Windows 10 IE 11 fix */
        -ms-perspective: 1200px;
    }
}

.editions-block {
    direction: rtl;
    position: relative;

    @include breakpoint(medium down) {
        margin: 0;
    }

    @include breakpoint(small down) {
        margin: 0 0 18px;
    }

    &__container {
        max-width: 1012px;

        @include breakpoint(medium down) {
            max-width: 641px;
        }

        @include breakpoint(small down) {
            width: 100%;
        }
    }

    &__control {
        @include absolute(249px);
        @include font(null, 3rem);
        @include size(50px);
        background: none;
        border: 0;
        color: $color-3--4;
        cursor: pointer;
        padding: 0;
        transform: translateY(-50%);
        z-index: 2;

        @include breakpoint(medium down) {
            top: 151px;
        }

        @include breakpoint(small down) {
            top: 128px;
        }

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        &.is-prev {
            left: 21px;

            @include breakpoint(medium down) {
                left: -14px;
            }

            @include breakpoint(small down) {
                left: -10px;
            }
        }

        &.is-next {
            right: 21px;

            @include breakpoint(medium down) {
                right: 31px;
            }

            @include breakpoint(small down) {
                right: -10px;
            }
        }
    }
}
