.account {
    //--ac-display: block;

    //display: var(--ac-display);
    margin: 0 auto 40px;

    @include breakpoint(medium down) {
        //--ac-display: flex;
        align-items: center;
        flex-direction: column;
    }

    @include breakpoint(small down) {
        margin: 0 0 84px;
    }

    .is-hidden {
        --ac-display: none;
    }

    .radio-checkbox__label {
        display: inline-block;

        a {
            color: var(--color-1--1);
            display: block;
            margin-top: 12px;
        }
    }

    &__actions {
        align-items: center;
        border-top: 1px solid $color-3--3;
        display: flex;
        flex-direction: column;
        margin: 70px 0 0;
        padding-top: 70px;

        @include breakpoint(medium down) {
            width: 100%;
        }

        .btn:not(:first-child) {
            margin-top: 12px;
        }
    }

    &__helper-wrap {
        align-items: center;
        display: flex;
    }

    &__helper-text {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-3--4;

        @include breakpoint(medium down) {
            font-size: 1.4rem;
            margin: -18px auto 30px;
        }

        a {
            @include on-event {
                text-decoration: none;
            }
        }
    }
}

.account-section {
    margin: 0;

    & + & {
        margin-top: 65px;
    }

    &.is-width-33 .section__title {
        margin-bottom: 40px;
    }

    .btn {
        align-items: flex-start;
        text-align: left;

        @include fa-icon-style(false) {
            margin-top: 1px;
        }
    }
}

.account-form {
    display: block;
    margin: 20px 0 55px;

    .btn {
        align-items: flex-start;
        letter-spacing: 1.3px;
        line-height: normal;
        padding: 22px 45px;
        text-align: left;

        &.is-sm-small span[class*=fa-] {
            margin-top: 3px;
        }
    }

    &:first-child {
        margin-top: 0;
    }
}

.account-info {
    @include font(var(--typo-1), 1.6rem, var(--fw-normal), italic);
    align-items: flex-start;
    color: $color-3--4;
    display: flex;
    justify-content: center;
    margin: 30px 0;

    svg {
        @include size(25px);
        fill: var(--color-2--1);
        margin: 0 10px 0 0;
    }

    span {
        line-height: 1.56;
    }
}
