.faq-item {
    $this: &;

    .is-list-ideas-box & {
        margin-bottom: 5px;
        width: 100%;

        &__response {
            background-color: var(--color-2--1);
            max-width: 1123px;
            padding: 29px 50px 22px 41px;

            &-title {
                @include font(var(--typo-1), 2rem, var(--fw-medium));
                color: $color-black;
            }

            &-text {
                @include font(var(--typo-1), 1.8rem, var(--fw-normal));
                color: $color-black;
                line-height: 1.6;
            }
        }
    }

    &__toggle {
        @include trs;
        background-color: $color-3--1;
        border: 0;
        color: $color-black;
        cursor: pointer;
        min-height: 135px;
        padding: 35px 130px 19px 35px;
        position: relative;
        text-align: left;
        width: 100%;

        .is-list-ideas-box & {
            padding: 35px 130px 32px 35px;
        }

        @include breakpoint(small down) {
            padding: 30px 40px 30px 30px;
        }
    }

    &__toggle-icon {
        @include trs;
        @include absolute(57px, 60px);
        color: var(--color-1--1);
        font-size: 2.2rem;

        @include breakpoint(small down) {
            right: 36px;
            top: 22px;
        }
    }

    &__toggle-category {
        @include trs;
        @include font(null, 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        display: block;
        letter-spacing: 2.52px;
        margin-bottom: 19px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.2rem;
            letter-spacing: 2.16px;
            padding-right: 40px;
        }
    }

    &__toggle-text {
        @include font(null, 2.4rem, var(--fw-bold));
        line-height: 1.15;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__block {
        display: none;
        overflow: hidden;
        z-index: 5;

        &.rte {
            margin: 0;

            &:not(.infowidget > &):not(.title) {
                margin-bottom: 15px;

                @include breakpoint(small down) {
                    margin-bottom: 64px;
                }
            }
        }
    }

    &__tags {
        display: flex;
        flex-wrap: wrap;
        gap: 34px;
        margin: 0 0 5px;
    }

    &__tag {
        @include font(null, 1.4rem, var(--fw-medium));
        color: var(--color-1--1);
        position: relative;
        text-transform: uppercase;

        &::before {
            @include absolute(0, -20px, null, null);
            @include size(1px, 19px);
            background-color: var(--color-2--1);
            content: '';
        }

        &:last-child {
            &::before {
                display: none;
            }
        }
    }

    &__documents {
        margin-bottom: 18px;
        margin-left: 25px;

        .telecharger__list {
            margin: 0 !important;
        }

        .telecharger__list-item {
            margin-bottom: 15px;

            &::before {
                content: none !important;
            }
        }

        .telecharger-item {
            align-items: flex-start;
            display: flex;
            gap: 148px;
            position: relative;
            width: 859px;

            @include breakpoint(medium down) {
                flex-direction: column;
                gap: 20px;
                width: 100%;
            }

            &__svg-wrap {
                @include size(19px,25px);
                @include absolute(0, null, null, -25px);

                svg {
                    @include size(100%);
                    fill: var(--color-2--1);
                }
            }

            &__title {
                @include font(var(--typo-1), 1.6rem !important, var(--fw-normal) !important);
                width: 418px;

                @include breakpoint(medium down) {
                    width: 100%;
                }
            }

            &__descr {
                align-items: center;
                display: flex;
                justify-content: space-between;
                width: 265px;

                @include breakpoint(medium down) {
                    align-items: flex-start;
                    flex-direction: column;
                    width: 100%;
                }
            }

            &__link {
                @include font(var(--typo-1), 1.2rem, var(--fw-normal));
                color: var(--color-1--1);
                display: flex;
                gap: 4px;
                line-height: 1.4;
                text-decoration: none;

                @include on-event {
                    background-color: $color-white;
                    color: var(--color-1--1);
                }
            }

            &__file-group {
                @include font(var(--typo-1), 1.4rem, var(--fw-normal));
                align-items: center;
                color: $color-3--5;
            }
        }
    }

    &__voteEnd {
        margin-top: 18px;

        &-title {
            @include font(var(--typo-1), 1.4rem, var(--fw-bold));
            color: var(--color-1--1);
        }
    }

    &__vote {
        display: flex;
        flex-direction: row;
        gap: 20px;
        margin-top: 28px;

        @include breakpoint(small down) {
            flex-direction: column;
            gap: 5px;
        }

        .btn {
            @include min-size(114px,43px);
            background-color: var(--color-1--1);
            color: $color-white;
            font-size: 1.2rem;
            line-height: 1.4;
            padding: 0 1.2rem;
            text-transform: uppercase;
        }

        &-results {
            display: flex;
            flex-direction: column;
            margin-right: 18px;
            margin-top: 4px;
    
            &-item {
                line-height: 100%;

                &-title {
                    @include font(var(--typo-1), 1.4rem, var(--fw-bold));
                    color: $color-3--5;
                    display: inline;
                    margin-right: 5px;
                }
    
                &-text {
                    @include font(var(--typo-1), 1.4rem, var(--fw-normal));
                    color: $color-3--5;
                    display: inline;
                }
    
                &-date {
                    @include font(var(--typo-1), 1.1rem, var(--fw-normal));
                    color: $color-3--5;
                    display: inline;
                    margin-right: 5px;
                }
            }
        }
    }

    &__comments {
        display: flex;
        position: relative;
    }

    .comment-link {
        @include font(var(--typo-1), 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        gap: 10px;
        line-height: 1.4;
        margin-top: 14px;
        position: relative;
        text-decoration: none;
        text-transform: uppercase;

        @include on-event {
            background-color: transparent;
            text-decoration: underline;
        }
    }

    &__wrapper {
        background-color: $color-3--1;
        padding: 0 35px 35px 63px;
        position: relative;

        .is-list-ideas-box & {
            background-color: $color-white;
            padding: 40px 26px 75px 37px;

            &::before {
                @include absolute(39px, null, 35px, 0);
                background-color: var(--color-1--1);
                content: '';
                width: 3px;
            }
        }

        @include breakpoint(small down) {
            padding: 0 30px 30px 50px;
        }

        &::before {
            @include absolute(4px, null, 35px, 35px);
            background-color: var(--color-1--1);
            content: '';
            width: 2px;
        }
    }

    & &__response {
        @include font(null, 2rem, var(--fw-medium));
        line-height: 1.4;
        margin: 0 0 3px;
    }

    & &__text {
        font-size: 1.8rem;
        line-height: 1.5;
        margin: 0 0 9px;

        .is-list-ideas-box & {
            margin: 0 0 35px;
        }

        @include breakpoint(medium down) {
            font-size: 1.6rem;
            line-height: 1.4;
        }

        @include breakpoint(small down) {
            font-size: 1.4rem;
            line-height: 1.3;
        }
    }

    &.is-open {
        #{$this} {
            margin-bottom: 0;

            &__toggle-icon {
                transform: scaleY(-1);
            }

            &__block {
                display: block;
            }
        }
    }
}
