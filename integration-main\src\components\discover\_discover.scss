.discover-content,
.discover-home {
    $this: &;

    &.is-full-width {
        #{$this} {
            &__content {
                @include full-width-block;
            }
        }
    }

    &__container {
        @extend %container;
    }

    & &__list-item {
        margin-bottom: 0;

        @include breakpoint(small down) {
            margin-bottom: 20px;
        }
    }
}

.discover-home {
    &__content {
        @include breakpoint(medium only) {
            padding: 0 22px;
        }
    }

    &.is-full-width {
        .title {
            @include breakpoint(large only) {
                margin-bottom: -90px;
                max-width: 100%;
                padding: 0 65px 38px 0;
                position: relative;

                &::before {
                    @include absolute(0, 0, null, null);
                    @include size(100vw, 100%);
                    background-color: $color-white;
                    content: '';
                    z-index: 1;
                }
            }
        }
    }
}
