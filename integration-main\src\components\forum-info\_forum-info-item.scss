.forum-info-item {
    @extend %link-block-context;
    align-items: flex-start;
    display: flex;
    margin-bottom: 18px;

    @include breakpoint(medium down) {
        flex-direction: column;
    }

    &__media {
        flex-shrink: 0;
        margin-right: 81px;

        @include breakpoint(medium down) {
            margin: 0 0 30px;
        }
    }

    &__picture {
        display: block;
        max-width: 250px;

        @include breakpoint(medium down) {
            max-width: 200px;
        }
    }

    &__content {
        flex: 1;
        padding-top: 3px;

        @include breakpoint(small down) {
            text-align: left;
        }
    }

    &__category {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: var(--color-1--1);
        display: block;
        margin-bottom: 19px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            margin-bottom: 10px;
        }
    }

    &__title {
        @include font(var(--typo-1), 3rem, var(--fw-bold));
        line-height: 1.2;
        margin: 0 0 21px;

        @include breakpoint(medium down) {
            font-size: 2.6rem;
            line-height: 1.3;
            margin: 0 0 17px;
        }

        @include breakpoint(small down) {
            font-size: 2.2rem;
            line-height: 1.3;
        }
    }

    &__teaser {
        @include font(var(--typo-1), 2rem, var(--fw-normal));
        line-height: 1.4;
        margin: 0 0 10px;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
            line-height: 1.3;
            margin: 0 0 10px;
        }

        @include breakpoint(small down) {
            font-size: 1.6rem;
            line-height: 1.3;
        }

        &::before {
            @include absolute(null, null, -11px, 0);
            @include size(34px, 5px);
            background-color: var(--color-2--1);
            content: '';
            display: block;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }
}
