.comment-item {
    $this: &;

    &__user-info {
        display: flex;
        gap: 37px;
        margin-bottom: 39px;

        .is-single-forum-elements & {
            gap: 29px;
        }
    }

    &__avatar {
        @include size(66px);
        align-items: center;
        background-color: var(--color-1--1);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        left: 1px;
        position: relative;
        top: -23px;

        svg {
            @include size(66px,38px);
            fill: $color-white;
        }
    }

    &__text {
        position: relative;

        &-date {
            @include font(var(--typo-1), 1.2rem, var(--fw-normal));
            color: $color-3--4;
            margin-bottom: 10px;

            .name {
                font-weight: var(--fw-bold);
            }
        }

        &-description {
            @include font(var(--typo-1), 1.6rem, var(--fw-normal));
            color: $color-black;
            min-height: 97px;
        }

        &::after {
            @include absolute(59px, null, null, -71px);
            @include size(3px, 32%);
            background-color: var(--color-1--1);
            content: '';
        }
    }

    &__text-arras {
        background-color: var(--color-2--1);
        min-height: 290px;
        padding: 28px 0 10px 40px;
        position: relative;
        width: 1099px;

        @include breakpoint(medium down) {
            padding: 28px 0 10px 26px;
            width: 100%;
        }

        @include breakpoint(small down) {
            padding: 15px 0 10px 13px;
        }

        &-date {
            @include font(var(--typo-1), 1.2rem, var(--fw-normal));
            color: var(--color-1--1);
            margin-bottom: 10px;

            .name {
                font-weight: var(--fw-bold);
            }
        }

        &-description {
            @include font(var(--typo-1), 1.6rem, var(--fw-normal));
            color: $color-black;
            margin-bottom: 18px;
            max-width: 1023px;
            min-height: 97px;
        }

        &::after {
            @include absolute(60px, null, null, -70px);
            @include size(3px, 47%);
            background-color: var(--color-1--1);
            content: '';
        }
    }
}
