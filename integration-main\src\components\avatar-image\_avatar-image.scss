.avatar-image {
    --avatar-image-size: auto;

    @include size(var(--avatar-image-size));
    display: block;

    @for $i from 1 through 8 {
        &.is-#{16 * $i}x#{16 * $i} {
            --avatar-image-size: #{16 * $i}px;
        }
    }

    &.is-rounded {
        background-color: $color-3--1;
        border-radius: 50%;
        overflow: hidden;

        img[src$=svg] {
            background: transparent;
        }
    }

    img {
        @include size(100%);
        @include object-fit();
        display: block;
    }
}
