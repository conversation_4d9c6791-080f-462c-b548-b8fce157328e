{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- import 'views/core-components/title.njk' as Title -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/link.njk' import Link -%}

{#
    Discover template.
#}
{% set defaultSettingsItem = {
    className: '',
    title: 'La piscine Tournesol François M<PERSON>ard à Achicourt fait peau neuve',
    category: 'GRAND PROJET',
    imageSizes: ['420x280?479', '600x400?768', '655x437'],
    tag: 'h3',
    link: 'EN SAVOIR PLUS'
} %}

{#
    DiscoverItem template.
    @param {object} settings - user settings object
#}
{%- macro DiscoverItem(settings = {}) -%}
    {# Default params  #}
    {% set params = Helpers.merge(defaultSettingsItem, settings) %}
    <article class="discover-item {{ params.className }}">
        <div class="discover-item__content-wrapper">
            <div class="discover-item__content">
                {% if params.title %}
                    <{{ params.tag }} class="item-title discover-item__title">
                        <span class="theme discover-item__category">
                            {{ params.category }}
                            <span class="sr-only"> : </span>
                        </span>
                        {{ params.title }}
                    </{{ params.tag }}>
                {% endif %}
                {% if params.link %}
                    {{ Link(
                        href = '#',
                        text = params.link,
                        className = 'btn is-primary is-small discover-item__link',
                        icon = false
                    ) }}
                {% endif %}
            </div>
        </div>
        {{ Image({
            className: 'discover-item__image',
            sizes: params.imageSizes,
            type: 'no-image' if (range(5, 20) | random) > 15 else 'default',
            serviceID: range(50) | random,
            alt: 'image alt'
        }) }}
    </article>
{%- endmacro -%}

{%- macro DiscoverList(
    imageSizes = ['420x280?479', '768x512?767', '655x437'],
    listClass = 'discover-list',
    itemClass = 'discover-list__item',
    count = 3,
    cols = 1
) -%}
    <ul class="list {{ listClass }}">
        {%- for listItem in range(count) -%}
            <li class="list__item {{ itemClass }}">
                {{ DiscoverItem ({
                    className: 'is-image-left' if loop.index % 2 !== 0,
                    imageSizes: imageSizes
                }) }}
            </li>
        {%- endfor -%}
    </ul>
{%- endmacro -%}

{%- macro DiscoverHome(
    className = '',
    titleText = 'Découvrir',
    moreButton = false
) -%}
    {% call Section(className = 'discover-home ' + className, container = 'discover-home__container') %}
        <div class="section__title discover-home__title">
            {{ Title.TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="section__content discover-home__content">
            {{ DiscoverList(
                imageSizes = ['420x280?479', '768x512?767', '600x400?1279', '960x640'] if className else ['420x280?479', '768x512?767', '655x437'],
                listClass = 'discover-home__list',
                itemClass = 'discover-home__list-item'
            ) }}
        </div>
        {% if moreButton %}
            <div class="section__more-links discover-home__more-links">
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listNews,
                        text = 'Toutes les actualites',
                        className = 'btn is-primary is-sm-small',
                        icon = false
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{%- macro DiscoverContent(
    className = '',
    titleText = 'Découvrir'
) -%}
    {% call Section(className = 'discover-content ' + className, container = false) %}
        <div class="section__title discover-content__title">
            {{ Title.TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content discover-content__content">
            {{ DiscoverList(
                imageSizes = ['420x280?479', '768x512?767', '600x400?1279', '960x640'] if className else ['420x280?479', '768x512?767', '655x437'],
                listClass = 'discover-content__list',
                itemClass = 'discover-content__list-item'
            ) }}
        </div>
    {% endcall %}
{%- endmacro -%}
