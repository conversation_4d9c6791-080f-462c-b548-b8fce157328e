{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/list.njk' import List -%}

{% set defaultSettings = {
    modifier: '',
    tagTitle: 'h2',
    tagTitleInfo: 'h3'
} %}

{%- macro ConsultationsItem(settings = {}) -%}
    {% set params = Helpers.merge(defaultSettings, settings) %}
    {% set titleId = range(100) | random %}
    <article class="consultations-item  {{ params.modifier }}">
        <div class="consultations-item__wrapper">
            <{{ params.tagTitle }} class="consultations-item__title" id="consultations-item-title-{{ titleId }}">
                <a href="/single-consultations.html" class="consultations-item__title-link">
                    <span class="underline">{{ lorem(range(4, 20) | random, 'words') | capitalize }}</span>
                </a>
            </{{ params.tagTitle }}>
            <p class="consultations-item__teaser">{{ lorem(3) }}</p>
        </div>
        <div class="consultations-item__info">
            <{{ params.tagTitleInfo }} class="consultations-item__info-title">
                Prendre rendez-vous
            </{{ params.tagTitleInfo }}>
            {{ Link(
                href = 'tel:0465715233',
                text = '04 65 71 52 33',
                textSrOnly = 'Téléphone',
                className = 'btn is-small',
                icon = 'far fa-phone',
                attrs = {
                    'aria-describedby': 'consultations-item-title-' + titleId
                }
            ) }}
            {{ Link(
                href = '#',
                text = 'En ligne',
                className = 'btn is-small',
                icon = 'far fa-mouse-pointer',
                attrs = {
                    'aria-describedby': 'consultations-item-title-' + titleId
                }
            ) }}
        </div>
    </article>
{%- endmacro -%}

{#
    ConsultationsList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} smCols - mobile columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro ConsultationsList(
    itemClass = 'has-mb-1',
    count = 8,
    cols = 1,
    smCols = 1
) -%}
    {% call List(
        itemClass = itemClass,
        count = count,
        cols = cols,
        smCols = smCols
        ) %}
        {{ ConsultationsItem({
            tagTitle: 'h3',
            tagTitleInfo: 'h4'
        }) }}
    {% endcall %}
{%- endmacro -%}

{% set defaultSettings = {
    modifier: '',
    tagTitle: 'h2'
} %}

{%- macro ConsultationsRegister(settings = {}) -%}
    {% set params = Helpers.merge(defaultSettings, settings) %}
    <div class="consultations-register {{ params.modifier }}">
        <{{ params.tagTitle }} class="consultations-register__title">Prenez rendez-vous</{{ params.tagTitle }}>
        <div class="consultations-register__contacts">
            {{ Link(
                href = '#',
                text = 'Prendre rendez-vous',
                className = 'btn is-primary is-large is-inverted',
                icon = 'far fa-user-md'
            ) }}
            {{ Link(
                href = 'tel:0465715233',
                text = '04 65 71 52 33',
                textSrOnly = 'Téléphone',
                className = 'btn is-primary is-large is-inverted',
                icon = 'far fa-phone'
            ) }}
        </div>
    </div>
{%- endmacro -%}


