{%- from 'views/utils/utils.njk' import setAttr, setAttributes, svg, wrapper as _wrapper with context -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/secondary.njk' import Loader -%}
{%- from 'components/password-visibility-manager/password-visibility-manager.njk' import PasswordVisibilityManager -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/icon.njk' import Icon -%}

{#
    Get wrapper class for form field. (Helper)
    @param {string} type - input type.
#}
{%- macro getWrapperClass(type = '') -%}
    {%- if type === 'date' -%}
        {%- set className = 'is-datewrapper' -%}
        {%- elif type === 'autocomplete' -%}
        {%- set className = 'js-autocomplete' -%}
        {%- elif type === 'file' -%}
        {%- set className = 'is-filewrapper js-file-field' -%}
        {%- elif type === 'file-default' -%}
        {%- set className = 'js-file-max-size' -%}
    {%- else -%}
        {%- set className = '' -%}
    {%- endif -%}
    {{ ' ' + className }}
{%- endmacro -%}

{#
    Insert input core input field with wrappers and core configuration.
    @param {string} type - input type.
    @param {string|boolean} label - input label text or false if label should be rendered.
    @param {string} textHelp - help text inside label.
    @param {string} textAlert - alert text inside label.
    @param {boolean} required - set input required attribute.
    @param {boolean} disabled - set input disabled attribute.
    @param {boolean} readonly - set input readonly attribute.
    @param {string} minDate - set input min date attribute.
    @param {string} maxDate - set input max date attribute.
    @param {string} placeholder - set input placeholder attribute.
    @param {string} value - custom value.
    @param {string} id - custom id.
    @param {object[]} options - set options for select.
    @param {string} name - set input name attribute.
    @param {string} wrapperModifier - add wrapper class name.
    @param {string} labelModifier - add label class name.
    @param {string} fieldClass - add class for input.
    @param {string} prepend - add prepend element with provided text.
    @param {string} append - add append element with provided text.
    @param {boolean} loader - add laoder to markup.
    @param {object} wrapperAttrs - add attributes for wrapper element.
    @param {object} customAttrs - add attributes for form element.
    @param {string} fileTypes - add validation by file type for file-default input.
    @param {string} fileSize - add validation by max file size for file-default input.
    @param {number} fileNumber - add validation by max number of files for file-default input.
#}
{%- macro FormField(
    type = 'text',
    label = 'Field label',
    inputType = '',
    textHelp = '',
    textAlert = '',
    textAlertAfter = '',
    required = false,
    disabled = false,
    readonly = false,
    enablePasswordVisibilityManager = false,
    minDate = '1000-01-01',
    maxDate = '3000-01-01',
    placeholder = '',
    value = '',
    title = '',
    id = false,
    options = [
        {
            value: '',
            text: '- Sélectionner -'
        },
        {
            value: '1',
            text: 'option 1'
        },
        {
            value: '2',
            text: 'option 2'
        },
        {
            value: '3',
            text: 'option 3'
        }
    ],
    name = '',
    wrapperModifier = '',
    labelModifier = '',
    fieldClass = '',
    prepend = '',
    append = '',
    customPlaceholder = '',
    loader = false,
    wrapperAttrs = {},
    customAttrs = {},
    labelAttrs = {},
    fileTypes = '.jpg, .gif, .png, .bmp, .rtf, .doc, .docx, .xls, .xlsx, .pdf, .zip',
    fileMaxSize = '1 Mo',
    fileNumber = 3,
    autocompleteAttr = '',
    labelId = false
) -%}
    {%- set uid = Helpers.unique() -%}
    {%- set fid = 'field-' + uid -%}
    {%- set descId = 'describedby-' + uid -%}
    {%- set fieldName = name or 'field_' + type + '_' + uid -%}

    {% if type === 'autocomplete' %}
        {% set autocompleteWrapperAttrs = {
            'data-json-path': 'js/data/test-autocomplete.json',
            'data-min-elements': '5',
            'data-max-elements': '7',
            'data-min-chars': '3',
            'data-mark-coincidence': 'on'
        } %}

        {% set wrapperAttrs = Helpers.merge(autocompleteWrapperAttrs, wrapperAttrs) %}
    {% endif %}

    <div class="form__field-wrapper {{ wrapperModifier + getWrapperClass(type) }}" {{ setAttributes(wrapperAttrs) }}>
        {%- if label !== false -%}
            <label
                for="{{ id or fid }}"
                class="form__label {{ labelModifier }}"
                {{ setAttr('id', labelId) if labelId }}
                {{ setAttributes(labelAttrs) }}
            >
                {{ label | safe }}
                {{- '<em class="required" aria-hidden="true">(Obligatoire)</em>' | safe if required -}}
                {{- ('<span class="text-help">' + textHelp + '</span>') | safe if textHelp -}}
                {{- ('<strong class="text-help is-alert">' + textAlert + '</strong>') | safe if textAlert -}}
            </label>
        {%- endif -%}
        {%- if prepend -%}
            <div class="form__field-wrapper-prepend">
                <span class="form__field-wrapper-content">{{ prepend }}</span>
            </div>
        {%- endif -%}
        {%- if type === 'textarea' -%}
            {# TEXTAREA #}
            <textarea
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                class="form__field {{ fieldClass }}"
                {{ setAttr('placeholder', placeholder if placeholder and not customPlaceholder) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttributes(customAttrs) }}
            >{{ value }}</textarea>
        {%- elif type === 'select' -%}
            {# SELECT #}
            <select
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                class="form__field {{ fieldClass }}"
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttributes(customAttrs) }}
            >
                {%- if caller -%}
                    {{ caller() }}
                {%- else -%}
                    {%- for option in options -%}
                        <option value="{{ option.value }}">{{ option.text }}</option>
                    {%- endfor -%}
                {%- endif -%}
            </select>
        {%- elif type === 'choices' -%}
            {# Choices element #}
            <select
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                class="form__field {{ fieldClass }}"
                {{ setAttr('data-choices', true) }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttributes(customAttrs) }}
            >
                {%- if caller -%}
                    {{ caller() }}
                {%- else -%}
                    {%- for option in options -%}
                        <option value="{{ option.value }}">{{ option.text }}</option>
                    {%- endfor -%}
                {%- endif -%}
            </select>
        {%- elif type === 'file' -%}
            {# FILE INPUT with drag and drop #}
            <div class="form__file-wrapper">
                <input
                    name="{{ fieldName }}"
                    id="{{ id or fid }}"
                    type="file"
                    multiple
                    class="form__field {{ fieldClass }}"
                    {{ setAttr('value', value) }}
                    {{ setAttr('disabled', disabled) }}
                    {{ setAttr('readonly', readonly) }}
                    {{ setAttr('required', required) }}
                    {{ setAttr('title', title) }}
                    {{ setAttr('aria-hidden', 'true') }}
                    {{ setAttr('tabindex', '-1') }}
                    {{ setAttributes(customAttrs) }}
                >
                <p
                    id="file-description-{{ id or fid }}"
                    class="form__file-field js-drop-area-block"
                    data-multiple-choosed="Fichiers sélectionnés: [N]"
                    role="button"
                    aria-labelledby="{{ labelId if labelId }} file-description-{{ id or fid }}"
                    tabindex="0"
                >
                    <span class="form__file-button">Choisir un fichier</span>
                    <span class="form__file-decoration"></span>
                    <span>Aucun fichier choisi</span>
                </p>
                <div class="form__files-list-wrapper js-file-list" data-related-field="{{ fid }}" aria-live="polite"></div>
            </div>

        {%- elif type === 'file-default' -%}
            {# FILE INPUT default #}
                <input
                    name="{{ fieldName }}"
                    id="{{ id or fid }}"
                    type="file"
                    accept="{{ fileTypes }}"
                    multiple
                    data-file-size="{{ fileMaxSize }}"
                    data-file-number="{{ fileNumber }}"
                    class="form__field {{ fieldClass }}"
                    {{ setAttr('value', value) }}
                    {{ setAttr('disabled', disabled) }}
                    {{ setAttr('readonly', readonly) }}
                    {{ setAttr('required', required) }}
                    {{ setAttr('title', title) }}
                    {{ setAttributes(customAttrs) }}
                >
        {%- elif type === 'geolocation' -%}
            {# GEOLOCATION #}
            <div class="form__geolocation-wrapper">
                {% call Button(
                    className = 'btn is-primary is-only-icon js-geocode',
                    icon = 'far fa-street-view',
                    tooltip = 'Géolocaliser ma position'
                    ) %}
                    <span class="ghost">Géolocaliser ma position</span>
                {% endcall %}
                <input
                    type="text"
                    name="{{ fieldName }}"
                    id="{{ id or fid }}"
                    class="form__field {{ fieldClass or 'js-geocode-full-address' }}"
                    {{ setAttr('placeholder', placeholder or 'Le Palais, Lorient, Brittany, Metropolitan France, 56360, France' if not customPlaceholder) }}
                    {{ setAttr('value', value) }}
                    {{ setAttr('disabled', disabled) }}
                    {{ setAttr('readonly', readonly) }}
                    {{ setAttr('required', required) }}
                    {{ setAttr('title', title) }}
                    {{ setAttr('aria-live', 'polite') }}
                    {{ setAttributes(customAttrs) }}
                >
            </div>
        {%- elif type === 'autocomplete' -%}
            {# AUTOCOMPLETE #}
            <input
                type="{{ inputType or 'text' }}"
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                class="form__field {{ fieldClass or 'js-autocomplete-input' }}"
                {{ setAttr('placeholder', placeholder or 'Input autocomplete' if not customPlaceholder) }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttr('aria-describedby', descId) if customPlaceholder }}
                {{ setAttributes(customAttrs) }}
                role="combobox"
                aria-autocomplete="list"
                aria-expanded="false"
                aria-activedescendant=""
            >
            {%- if loader -%}
                {{ Loader(className = 'loader-grow', state = '') }}
            {%- endif -%}
        {%- elif type === 'email' -%}
            <input
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                type="email"
                class="form__field {{ fieldClass }}"
                {{ setAttr('placeholder', placeholder if placeholder and not customPlaceholder) }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttr('autocomplete', autocompleteAttr or 'email') }}
                {{ setAttributes(customAttrs) }}
            >
        {%- elif type === 'tel' -%}
            {# PHONE #}
            <input
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                type="tel"
                pattern="[0-9]{10}|[0-9| ]{14}"
                class="form__field {{ fieldClass }}"
                {{ setAttr('placeholder', placeholder if placeholder and not customPlaceholder) }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttr('autocomplete', 'tel-national') }}
                {{ setAttributes(customAttrs) }}
            >
        {%- elif type === 'password' -%}
            {# PASSWORD #}
            {%- if enablePasswordVisibilityManager -%}
                <div class="form__password-visibility-manager-wrapper">
                {{ PasswordVisibilityManager(targetSelector = '#' + id + ':global' or fid + ':global') }}
            {%- endif -%}
                <input
                    name="{{ fieldName }}"
                    id="{{ id or fid }}"
                    type="password"
                    class="form__field {{ fieldClass }}"
                    {{ setAttr('placeholder', placeholder if placeholder and not customPlaceholder) }}
                    {{ setAttr('value', value) }}
                    {{ setAttr('disabled', disabled) }}
                    {{ setAttr('readonly', readonly) }}
                    {{ setAttr('required', required) }}
                    {{ setAttr('title', title) }}
                    {{ setAttributes(customAttrs) }}
                >
            {%- if enablePasswordVisibilityManager -%}
                </div>
            {%- endif -%}
        {%- elif type === 'date' -%}
            {# DATE #}
            <input
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                type="date"
                class="form__field {{ fieldClass }}"
                {{ setAttr('title', title or 'jj/mm/aaaa') }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('min', minDate) }}
                {{ setAttr('max', maxDate) }}
                {{ setAttributes(customAttrs) }}
            >
        {%- elif type === 'time' -%}
            {# TIME #}
            <input
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                type="time"
                class="form__field {{ fieldClass }}"
                {{ setAttr('placeholder', placeholder if placeholder and not customPlaceholder) }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttributes(customAttrs) }}
            >
        {%- elif type === 'url' -%}
            <input
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                type="url"
                class="form__field {{ fieldClass }}"
                {{ setAttr('placeholder', placeholder if placeholder and not customPlaceholder) }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttributes(customAttrs) }}
            >
        {%- else -%}
            {# CUSTOM #}
            <input
                type="{{ type }}"
                name="{{ fieldName }}"
                id="{{ id or fid }}"
                class="form__field {{ fieldClass }}"
                {{ setAttr('placeholder', placeholder if placeholder and not customPlaceholder) }}
                {{ setAttr('value', value) }}
                {{ setAttr('disabled', disabled) }}
                {{ setAttr('readonly', readonly) }}
                {{ setAttr('required', required) }}
                {{ setAttr('title', title) }}
                {{ setAttr('aria-describedby', 'text-alert-' + uid) if textAlertAfter }}
                {{ setAttributes(customAttrs) }}
            >
        {%- endif -%}
        {%- if append -%}
            <div class="form__field-wrapper-append">
                <span class="form__field-wrapper-content">{{ append }}</span>
            </div>
        {%- endif -%}
        {%- if customPlaceholder -%}
            <span class="form__field-placeholder" aria-hidden="true" id="{{ descId }}">
                {{ customPlaceholder }}
            </span>
        {%- endif -%}
        {%- if textAlertAfter -%}
            <p class="text-help is-alert" id="text-alert-{{ uid }}" aria-hidden="true">{{ textAlertAfter }}</p>
        {%- endif -%}
        {{ caller() if caller }}
    </div>
{%- endmacro -%}

{#
    Insert checkbox or radio input.
    @param {string} className - add class for wrapper.
    @param {string} fieldClass - add class for input.
    @param {sting} type - field type (checkbox|radio).
    @param {string} name - set input name attribute.
    @param {string|boolean} label - input label text or false if label should be rendered.
    @param {string} textHelp - help text inside label.
    @param {string} textAlert - alert text inside label.
    @param {boolean} required - set input required attribute.
    @param {boolean} disabled - set input disabled attribute.
    @param {boolean} checked - set input checked attribute.
    @param {string} value - set input value attribute.
    @param {string} wrapperTag - set tag name for wrapper element.
    @param {boolean} innerCall - insert caller inside wrapper or outside.
#}
{%- macro RadioCheckbox(
    className = '',
    fieldClass = '',
    type = 'checkbox',
    name = '',
    id = '',
    label = 'Libellé du champ',
    textHelp = '',
    textAlert = '',
    value = '',
    required = false,
    disabled = false,
    checked = false,
    wrapperTag = 'div',
    innerCall = false,
    disableRequiredLabel = true,
    groupButton = false
) -%}
    {%- set uid = Helpers.unique() %}
    {%- set fid = id or 'chr-generated-' + uid -%}
    {%- set fieldName = name or 'chr_generated_' + uid -%}

    <{{ wrapperTag }} class="form__radio-checkbox radio-checkbox {{ className }}">
        <input
            type="{{ type }}"
            id="{{ fid }}"
            name="{{ fieldName }}"
            class="radio-checkbox__input {{ fieldClass }}"
            {{ setAttr('required', required) }}
            {{ setAttr('disabled', disabled) }}
            {{ setAttr('checked', checked) }}
            {{ setAttr('value', value) }}
        >
        <label for="{{ fid }}" class="radio-checkbox__label {{ labelModifier }}">
            {{ label | safe }}
            {{- '<em class="required" aria-hidden="true">(Obligatoire)</em>' | safe if required and not disableRequiredLabel -}}
            {{- ('<span class="text-help">' + textHelp + '</span>') | safe if textHelp -}}
            {{- ('<strong class="text-help is-alert">' + textAlert + '</strong>') | safe if textAlert -}}
        </label>
        {{ caller() if caller and innerCall }}
    </{{ wrapperTag }}>
    {{ caller() if caller and not innerCall }}
{%- endmacro -%}

{#
    Set checkbox or radio inputs group.
    @param {string} legend - legend text.
    @param {boolean} hideLegend - hide legend text with ghost pattern or not.
    @param {string} legendTag - tag name for legend element.
#}
{% macro RadioCheckboxGroup(
    className = '',
    legend = 'Group de la checkbox/radio',
    hideLegend = false,
    legendTag = 'legend',
    required = false,
    hideRequired = true,
    btnReset = false,
    legendPopupIcon = ''
) %}
    {% set fieldsetTag = 'fieldset' if legendTag === 'legend' else 'div' %}
    {% set role = 'group' if legendTag !== 'legend' %}
    {% set described = 'generated-' + Helpers.unique() if legendTag !== 'legend' %}

    <{{ fieldsetTag }} class="form__radio-checkbox radio-checkbox {{ 'js-validator-group' if required }} {{ className }}" {{ setAttr('role', role) }} {{ setAttr('aria-describedby', described) }}>
        {% if legend %}
            <{{ legendTag }} class="legend form__legend{{ ' ghost' if hideLegend }}" {{ setAttr('id', described) }}>
                {{ legend | safe }}
                {% if required %}
                    <em class="required" {{- 'aria-hidden="true"' | safe if hideRequired -}}> (Obligatoire)</em>
                {% endif %}
                {% if legendPopupIcon %}
                    {{ Link(
                        className = 'legend__btn',
                        href = '#',
                        icon = legendPopupIcon,
                        tooltip = 'Informations complémentaires',
                        text = '',
                        textSrOnly = 'informations complémentaires',
                        attrs = {
                            'data-src': '#legend-popup',
                            'data-fancybox': 'legend',
                            'data-small-btn': 'false',
                            'data-toolbar': 'false',
                            'aria-haspopup': 'dialog',
                            'aria-label': 'indice contextuel',
                            'data-fancybox-body-class': 'is-popup-opened'
                        }
                    ) }}
                {% endif %}
            </{{ legendTag }}>
        {% endif %}
        {{ caller() if caller }}
        {% if btnReset %}
            {{ Button(
                className = 'btn is-link js-choice-reset',
                type = 'button',
                text = 'Effacer la sélection',
                icon = false
            ) }}
        {% endif %}
    </{{ fieldsetTag }}>
{% endmacro %}


{#
    Insert single grouped field.
    @param {string} className - add class for group.
    @param {string} innerClassName - add class for inner group wrapper.
    @param {string|boolean} label - input label text or false if label should be rendered.
    @param {string} textHelp - help text inside label.
    @param {string} textAlert - alert text inside label.
    @param {boolean} required - set input required attribute.
    @param {string} placeholder - set input placeholder attribute.
    @param {object[]} options - set options for select.
    @param {string} name - set input name attribute.
    @param {string} wrapperModifier - add wrapper class name.
    @param {string} labelModifier - add label class name.
    @param {string} fieldClass - add class for input.
    @param {string} prepend - add prepend element with provided text.
    @param {string} append - add append element with provided text.
#}
{% macro FieldGroup(
    type = 'text',
    className = '',
    innerClassName = '',
    label = 'Field label',
    inputType = '',
    textHelp = '',
    textAlert = '',
    placeholder = '',
    required = false,
    options = [
        {
            value: '',
            text: '- select -'
        },
        {
            value: '1',
            text: 'option 1'
        },
        {
            value: '2',
            text: 'option 2'
        },
        {
            value: '3',
            text: 'option 3'
        }
    ],
    name = '',
    wrapperModifier = '',
    labelModifier = '',
    fieldClass = '',
    prepend = '',
    append = '',
    customPlaceholder = '',
    wrapperAttrs = {},
    customAttrs = {},
    labelAttrs = {}
) %}
    {%- set uid = Helpers.unique() -%}
    {%- set fid = 'field-' + uid -%}
    <div class="form__controls-group {{ className }}">
        <label for="{{ fid }}" class="form__label {{ labelModifier }}" {{ setAttributes(labelAttrs) }}>
            {{ label }}
            {{- '<em class="required" aria-hidden="true">(Obligatoire)</em>' | safe if required -}}
            {{- ('<span class="text-help">' + textHelp + '</span>') | safe if textHelp -}}
            {{- ('<strong class="text-help is-alert">' + textAlert + '</strong>') | safe if textAlert -}}
        </label>
        {% call _wrapper(className = innerClassName) %}
            {{ FormField(
                type = type,
                label = false,
                inputType = inputType,
                placeholder = placeholder,
                required = required,
                wrapperModifier = wrapperModifier,
                fieldClass = fieldClass,
                name = name,
                options = options,
                prepend = prepend,
                append = append,
                customPlaceholder = customPlaceholder,
                wrapperAttrs = wrapperAttrs,
                customAttrs = customAttrs,
                labelAttrs = labelAttrs,
                id = fid
            ) }}
            {{ caller() if caller }}
        {% endcall %}
    </div>
{% endmacro %}


{#
    Insert group of fields.
    Group represents a fields row.
    @param {string} className - add class for group.
    @param {string} columnClass - set class for inner columns.
    @param {object[]|array[]} fields - fields array.
#}
{%- macro FormGroup(
    className = '',
    columnClass = 'col-xs',
    fields = [
        {
            type: 'text',
            label: '',
            columnClass: 'col-xs'
        }
    ]
) -%}
    <div class="form__fields-group {{ className }}">
        {%- if fields and not caller -%}
            {%- for field in fields -%}
                {%- if field.type or field.columnClass or field.label -%}
                    <div class="{{ field.columnClass or columnClass }}">
                        {{ FormField(type = field.type, label = field.label) }}
                    </div>
                {%- else -%}
                    <div class="{{ columnClass }}">
                        {{ FormField(type = field) }}
                    </div>
                {%- endif -%}
            {%- endfor -%}
        {%- else -%}
            {{ caller() if caller }}
        {%- endif -%}
    </div>
{%- endmacro -%}

{#
    Render categories tree, tree of checkboxes.
    @param {string} wrapperClass - add class name for wrapper element.
    @param {string} wrapperTag - set tag name for wrapper element.
    @param {boolean} groupButton - set groupButton.
    @param {object[]} data - data array.
#}
{%- macro CategoriesTree(
    wrapperClass = 'categories-tree js-cat-root',
    wrapperTag = 'ul',
    groupButton = false,
    data = [
        {
            name: 'Category 1',
            subcategories: [
                {
                    name: 'Subcategory 1-1',
                    subcategories: [
                        { name: 'Subcategory 1-1-1', checked: true },
                        { name: 'Subcategory 1-2-1' },
                        { name: 'Subcategory 1-3-1' }
                    ]
                },
                { name: 'Subcategory 1-2' }
            ]
        },
        {
            name: 'Category 2',
            subcategories: [
                { name: 'Subcategory 2-1' },
                { name: 'Subcategory 2-2' }
            ]
        },
        { name: 'Category 3' }
    ]
) -%}
    {%- call _wrapper(className = wrapperClass, tag = wrapperTag) -%}
        {% set uid = 'sd-id-' + Helpers.unique() %}
        {%- for category in data -%}
            <li class="categories-tree__item js-cat-branch">
                {%- call RadioCheckbox(
                    type = 'checkbox',
                    checked = category.checked,
                    value = 'value-generated-' + Helpers.unique(),
                    label = category.name,
                    groupButton = groupButton
                ) -%}
                    {% if groupButton and category.subcategories %}
                        {%- call Button(className = 'btn is-only-icon is-link is-small categories-tree__toggle is-tooltip-right', tooltip = 'Sous-catégories de ' + category.name, icon = 'far fa-angle-down', attrs = {'data-sd-toggle': uid}) -%}
                            <span class="ghost">Sous-catégories de {{ category.name }}</span>
                        {%- endcall -%}
                    {% endif %}
                    {%- if category.subcategories -%}
                        <ul class="categories-tree__inner js-cat-subtree" {{ setAttr('data-sd-content', uid) if groupButton }}>
                            {{ CategoriesTree(wrapperClass, wrapperTag, false, category.subcategories) }}
                        </ul>
                    {%- endif -%}
                {%- endcall -%}
            </li>
        {%- endfor -%}
    {%- endcall -%}
{%- endmacro -%}

{#
    Insert multiselect element.
    @param {string} legend - add legend text.
    @param {string} placeholder - add multiselect placeholder text.
    @param {string} type - set multiselect type.
    @param {boolean} showCount - show count of checked elements instead names.
    @param {string} prefix - add prefix for checked elements.
    @param {string} suffix - add suffix for checked elements.
    @param {boolean} hideLegend - hide legend text with ghost pattern.
    @param {object[]|boolean} data - provide data for multiselect (categories tree data).
    @param {boolean} groupButton - set groupButton for categories in multiselect.
    @param {string} modifier - set modifier for multiselect.
#}
{%- macro Multiselect(
    legend = 'Categories',
    placeholder = 'Toutes les thématiques',
    type = 'dropdown',
    showCount = true,
    content = '{count} options sélectionnées',
    hideLegend = false,
    data = false,
    state = '',
    dropdown = true,
    groupButton = false,
    modifier = '',
    wrapperModifier = '',
    loadHTML = './multiselect-list.html'
) -%}
    {%- set uid = Helpers.unique() -%}
    <div class="form__field-wrapper {{ wrapperModifier }}">
        <div class="multiselect-wrapper" role="group" aria-labelledby="multiselect-legend-{{ uid }}">
            <p id="multiselect-legend-{{ uid }}" class="label multiselect-wrapper__legend{{ ' ghost' if hideLegend }}">{{ legend }}</p>
            <div
                class="multiselect {{ state }} {{ modifier }}"
                {{ setAttr('data-type', type) }}
                {{ setAttr('data-show-count', showCount) }}
                {{ setAttr('data-content', content) }}
                {{ setAttr('data-placeholder', placeholder) }}
                {{ setAttr('data-load-ul', loadHTML) }}
            >
                {%- if dropdown -%}
                    <button class="multiselect__toggle js-dropdown-toggle" type="button">{{ placeholder }}</button>
                    <div class="multiselect__dropdown js-dropdown-block" tabindex="-1">
                        {%- if loadHTML -%}
                            <p class="multiselect__loader">Chargement des données...</p>
                        {%- else -%}
                            {{ CategoriesTree(data = data) if data !== false else CategoriesTree() }}
                        {%- endif -%}
                    </div>
                {%- else -%}
                    {{ CategoriesTree(data = data, groupButton = groupButton) if data !== false else CategoriesTree(groupButton = groupButton) }}
                {%- endif -%}
            </div>
            <input type="hidden" class="js-multiselect-value" name="multiselect_dataid_{{ uid }}">
        </div>
    </div>
{%- endmacro -%}

{#
    Insert form actions.
    @param {string} className - add custom class name for form actions
#}
{%- macro FormActions(className = '') -%}
    <div class="form__actions {{ className }}">
        {{ caller() if caller }}
    </div>
{%- endmacro -%}

{#
    Insert avatars group.
    @param {string} legend - legend for group.
#}
{%- macro FormAvatarsGroup(legend = 'Avatar', legendClassName = '') -%}
    {%- if legend -%}
        <fieldset class="form__avatars-group">
            <legend class="label form__avatars-legend {{ legendClassName }}">{{ legend }}</legend>
            <div class="form__avatars-fields">
                {{ caller() }}
            </div>
        </fieldset>
    {%- else -%}
        {{ caller() }}
    {%- endif -%}
{%- endmacro -%}

{#
    Insert avatar.
    @param {string} icon - icon name in images/icons/ folder
#}
{%- macro FormAvatar(icon, checked = false) -%}
    {%- set uid = Helpers.unique() -%}
    {%- set id = 'field-avatar-' + uid -%}
    <div class="form__avatar-field">
        <div class="form-avatar">
            <input type="radio" class="form-avatar__input" name="avatar" id="{{ id }}" value="{{ uid }}" {{ setAttr('checked', checked) }}>
            <label for="{{ id }}" class="form-avatar__label">
                {{ svg('icons/' + icon, 43, 43) }}
            </label>
        </div>
    </div>
{%- endmacro -%}

{#
    Insert form element.
    @param {string} className - add class name for form element.
    @param {string} legend - add legend text.
    @param {string} legendTag - set tag name for legend element.
    @param {string} fieldsetClassName - add class name for fieldset element.
    @param {string} legendClassName - add class name for legend element.
    @param {object} attrs - object with attributes.
#}
{% macro FormWrapper(
    tag = 'form',
    className = 'js-validator-form',
    legend = 'Form legend',
    legendTag = 'legend',
    fieldsetClassName = '',
    legendClassName = '',
    legendId = '',
    id = '',
    attrs = {},
    method = 'POST',
    action = '/'
) %}

    {% set fieldsetTag = 'fieldset' if legendTag === 'legend' else 'div' %}
    {% set described = legendId or 'generated-' + Helpers.unique() if legendTag !== 'legend' %}

    <{{tag}} class="form {{ className }}" {%- if tag == 'form' -%} {{ setAttr('method', method) }} action="{{ action }}" enctype="multipart/form-data" {%- endif -%} {{ setAttr('id', id) }} {{ setAttributes(attrs) }}>
        {%- if legend -%}
            <{{ fieldsetTag }}
                class="form__fieldset {{ fieldsetClassName }}"
            >
                <{{ legendTag }} class="legend form__legend {{ legendClassName }}" {{ setAttr('id', described) }}>{{ legend | safe }}</{{ legendTag }}>
                {{ caller() if caller }}
            </{{ fieldsetTag }}>
        {%- else -%}
            {{ caller() if caller }}
        {%- endif -%}
    </{{tag}}>
{% endmacro %}

{#
    Insert password field with rules list.
    @param {string} label - add label for field.
    @param {string} required - add required attribute.
#}
{%- macro SuperPassword(
    label = 'Mot de passe ',
    placeholder,
    required = true
) -%}
    <div class="form__password-field">
        <div class="form-password">
            {%- call FormField(
                type = 'password',
                label = label,
                required = required,
                placeholder = placeholder,
                fieldClass = 'js-validator-password',
                enablePasswordVisibilityManager = true,
                id = 'identical-field-password',
                customAttrs = {
                    'pattern': '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-=]).{16,}$',
                    'autocomplete': 'new-password',
                    'aria-describedby': 'identical-field-password-text-help password-rules'
                }
            ) -%}
                <p id="identical-field-password-text-help" class="text-help" aria-hidden="true">Votre mot de passe doit contenir au minimum :</p>
                <ul class="password-rule" id="password-rules" aria-hidden="true">
                    {%- for item in [
                        {
                            name: 'characters',
                            value: '16',
                            text: 'caractères'
                        },
                        {
                            name: 'capital',
                            value: '1',
                            text: 'majuscule'
                        },
                        {
                            name: 'lowercase',
                            value: '1',
                            text: 'minuscule'
                        },
                        {
                            name: 'digit',
                            value: '1',
                            text: 'chiffre'
                        },
                        {
                            name: 'symbol',
                            value: '1',
                            text: 'symbole'
                        }
                    ] %}
                        <li class="password-rule__item" data-password-{{ item.name }}="{{ item.value }}">
                            <span class="password-rule__icon far fa-times" aria-hidden="true" title="Invalidé"></span>
                            <span class="sr-only">Ne contient pas</span>
                            <span class="password-rule__text"> {{ item.value + ' ' + item.text }}</span>
                        </li>
                    {%- endfor %}
                </ul>
            {%- endcall -%}
        </div>
    </div>
{%- endmacro -%}

{% macro SelectList(
    selectLinks = [
        'Thématique (2)',
        'Thématique (10)',
        'Thématique (3)',
        'Thématique (7)'
    ],
    modifier = false
    )
%}
{% set uid = Helpers.unique() %}
    <ul class="search-aside__select {{ modifier if modifier }}">
        {% for selectItem in selectLinks %}
            <li class="search-aside__select-item">
                <input type="checkbox" id="chr-generated-{{ uid }}-{{ loop.index }}" name="chr_generated_{{ uid }}-{{ loop.index }}" class="radio-checkbox__input ghost">
                <label for="chr-generated-{{ uid }}-{{ loop.index }}">{{ selectItem }}</label>
            </li>
        {% endfor %}
    </ul>
{% endmacro %}

{#
    Insert a date range picker with two date input fields (from and to) and calendar icons.
    @param {string} label - add label for the date range field.
    @param {string} fromLabel - label for the 'From' date field.
    @param {string} toLabel - label for the 'To' date field.
    @param {string} placeholder - placeholder text for the input fields.
    @param {boolean} required - set input required attribute.
    @param {string} wrapperClass - add class for wrapper element.
    @param {object} customAttrs - add custom attributes for the input fields.
#}
{% macro DateRangePicker(
    label = 'Sélectionner une date',
    fromLabel = 'Du',
    toLabel = 'Au',
    placeholder = '',
    required = false,
    wrapperClass = '',
    customAttrs = {}
) %}
{%- set uid = Helpers.unique() -%}
    <div class="form__field-wrapper {{ wrapperClass }}">
        {%- if label -%}
            <label class="form__label">{{ label }}</label>
        {%- endif -%}
        <div class="form__date-range">
            <div class="form__date-field">
                <label for="date-from" class="form__date-label">{{ fromLabel }}</label>
                <div class="form__date-input-wrapper">
                    <input
                        type="date"
                        id="date-from"
                        name="field_date_start_{{uid}}"
                        class="form__field form__date-input without-icon"
                        placeholder="{{ placeholder }}"
                        {{ setAttr('required', required) }}
                        {{ setAttributes(customAttrs) }},
                        onfocus="(this.type='date')"
                    >
                    <span class="form__date-icon">
                        <i class="far fa-calendar-alt"></i>
                    </span>
                </div>
            </div>

            <div class="form__date-field">
                <label for="date-to" class="form__date-label">{{ toLabel }}</label>
                <div class="form__date-input-wrapper">
                    <input
                        type="date"
                        id="date-to"
                        name="field_date_to_{{uid}}"
                        class="form__field form__date-input without-icon"
                        placeholder="{{ placeholder }}"
                        {{ setAttr('required', required) }}
                        {{ setAttributes(customAttrs) }},
                        onfocus="(this.type='date')"
                    >
                    <span class="form__date-icon">
                        <i class="far fa-calendar-alt"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
{% endmacro %}

