/*
 * @name flash-info is-popup.
 */

.flash-info {
    $this: &;

    &.is-popup {
        @include fixed(0, 0, 0, 0);
        align-items: center;
        background-color: rgba($color-black, 0.8);
        padding: 0 60px;
        z-index: 101;

        @include breakpoint(small down) {
            padding: 0 20px;
        }

        &.is-visible {
            display: flex;
        }

        #{$this}__wrapper {
            background-color: var(--color-1--1);
            display: block;
            max-width: 756px;
            padding: 48px 70px 76px;
            position: relative;

            @include breakpoint(medium down) {
                padding: 48px 60px 60px;
            }

            @include breakpoint(small down) {
                padding: 30px 23px 45px;
            }
        }

        #{$this}__title {
            margin: 0 0 25px;

            @include breakpoint(medium only) {
                justify-content: flex-start;
            }
        }

        #{$this}__title-text {
            @include focus-outline;
            max-width: none;

            @include breakpoint(small down) {
                font-size: 2.8rem;
                line-height: 3.2rem;
            }
        }

        #{$this}__title-svg {
            @include size(47px);

            @include breakpoint(small down) {
                @include size(43px);
            }
        }
    }
}

body.js-flash-info-overflow {
    overflow: hidden;
}
