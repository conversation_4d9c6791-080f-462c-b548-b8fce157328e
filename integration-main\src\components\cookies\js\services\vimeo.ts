import { objectToQueryString } from '../utils/transform.utils';
import { setCookiePlaceholder } from '../utils/cookie.utils';

declare let tarteaucitron;

/**
 * Custom youtube cookie service for tarteaucitron plugin.
 */
export default class VimeoCookie {
    public constructor() {
        try {
            this.init();
        } catch (error) {
            console.error(error);
        }
    }

    /**
     * Set service cookies according dependencies
     */
    public update(): void {
        // Add update logic here.
    }

    /**
     * Add event listeners for service.
     */
    private addListeners(): void {
        // Add listeners here.
    }

    /**
     * Handle service items if cookies accepted.
     */
    private acceptedHandler(): void {
        const elements = [...document.querySelectorAll('.js-vimeo-player-stratis')];

        elements.forEach(elem => {
            const elemAttrs = {
                videoID: tarteaucitron.getElemAttr(elem, 'videoID') || tarteaucitron.getElemAttr(elem, 'data-videoID'),
                width: tarteaucitron.getElemAttr(elem, 'width') || tarteaucitron.getElemAttr(elem, 'data-width'),
                height: tarteaucitron.getElemAttr(elem, 'height') || tarteaucitron.getElemAttr(elem, 'data-height'),
            };

            if (!elemAttrs.videoID) {
                return '';
            }

            const videoParams = objectToQueryString({
                autopause: tarteaucitron.getElemAttr(elem, 'data-autopause'),
                autoplay: tarteaucitron.getElemAttr(elem, 'data-autoplay') || tarteaucitron.getElemAttr(elem, 'autoplay'),
                background: tarteaucitron.getElemAttr(elem, 'data-background'),
                byline: tarteaucitron.getElemAttr(elem, 'data-byline') || tarteaucitron.getElemAttr(elem, 'byline'),
                color: tarteaucitron.getElemAttr(elem, 'data-color'),
                controls: tarteaucitron.getElemAttr(elem, 'data-controls'),
                loop: tarteaucitron.getElemAttr(elem, 'data-loop') || tarteaucitron.getElemAttr(elem, 'loop'),
                maxheight: tarteaucitron.getElemAttr(elem, 'data-maxheight'),
                maxwidth: tarteaucitron.getElemAttr(elem, 'data-maxwidth'),
                muted: tarteaucitron.getElemAttr(elem, 'data-muted'),
                playsinline: tarteaucitron.getElemAttr(elem, 'data-playsinline'),
                speed: tarteaucitron.getElemAttr(elem, 'data-speed'),
                transparent: tarteaucitron.getElemAttr(elem, 'data-transparent'),
                portrait: tarteaucitron.getElemAttr(elem, 'data-portrait') || tarteaucitron.getElemAttr(elem, 'portrait'),
                title: tarteaucitron.getElemAttr(elem, 'data-title') || tarteaucitron.getElemAttr(elem, 'title'),
            });

            // eslint-disable-next-line max-len
            elem.innerHTML = `<iframe type="text/html" loading="lazy" src="//player.vimeo.com/video/${elemAttrs.videoID + videoParams}" frameborder="0"  webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>`;
        });
    }

    /**
     * Handle service items if cookies denied.
     */
    private deniedHandler(): void {
        const id = 'stratis-vimeo';

        const elements = [...document.querySelectorAll('.js-vimeo-player-stratis')];

        elements.forEach(elem => {
            const placeholder = setCookiePlaceholder(id, elem, true);
            elem.appendChild(placeholder);
        });
    }

    /**
     * Init service.
     */
    private init(): void {
        tarteaucitron.services['stratis-vimeo'] = {
            key: 'stratis-vimeo',
            type: 'video',
            name: 'Vimeo',
            readmoreLink: 'https://tarteaucitron.io/fr/service/vimeo/',
            uri: 'https://vimeo.com/privacy',
            needConsent: true,
            cookies: ['__utmt_player', '__utma', '__utmb', '__utmc', '__utmv', 'vuid', '__utmz', 'player'],
            js: () => this.acceptedHandler(),
            fallback: () => this.deniedHandler(),
        };

        this.addListeners();
    }
}
