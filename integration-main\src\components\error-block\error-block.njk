{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/utils/utils.njk' import svg, getImagePath -%}
{%- from 'views/core-components/link.njk' import Link -%}

{#
    ErrorBlock template.
#}
{%- macro ErrorBlock( image = '', width = '', height = '' ) -%}
    <div class="error-block">
        <div class="error-block__wrapper">
            {%- if image -%}
                <div class="error-block__picture" aria-hidden="true">
                    {{ svg(image, width, height) }}
                </div>
            {%- else -%}
                {{ Image({
                    sizes: ['164x164?767', '328x328?1279', '434x434'],
                    className: 'error-block__picture is-custom',
                    serviceID: range(50) | random
                }) }}
            {%- endif -%}
            <div class="error-block__content">
                <h2 class="error-block__title">Ouuups...</h2>
                <p class="error-block__subtitle">
                    Nous sommes désolés, cette page
                    <span>est introuvable!</span>
                </p>
                <p class="error-block__teaser">L'URL saisie est peut-être erronée ou bien la page n'existe pas ou n'est plus en ligne… Mais pas d'inquiétude, nous allons vous aider à retrouver votre chemin :)</p>
                <div class="error-block__buttons buttons-group">
                    {{ Link(
                        href = '#',
                        text = 'Retour à l\'accueil',
                        className = 'btn is-primary',
                        icon = 'far fa-home'
                    ) }}
                    {{ Link(
                        href = '#',
                        text = 'Rechercher',
                        className = 'btn',
                        icon = 'far fa-search'
                    ) }}
                </div>
            </div>
        </div>
        <p class="error-block__text-bottom">
            Pour toute question nous vous remercions de bien vouloir
            <a href="#">nous contacter par le formulaire du site</a>
        </p>
    </div>
{%- endmacro -%}
