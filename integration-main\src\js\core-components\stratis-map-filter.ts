import { debounce } from '@core/utils/function.utils';
import { addClass, removeClass } from '@core/utils/class.utils';
import StratisMap from '@components/map/js/map';

/**
 * Dynamic map filter component for real-time marker filtering
 */
export default class StratisMapFilter {
    private mapInstance: StratisMap | null = null;
    private searchInput: HTMLInputElement | null = null;
    private clearButton: HTMLButtonElement | null = null;
    private searchContainer: HTMLElement | null = null;
    private isInitialized = false;

    // Debounced search function
    private debouncedSearch: Function;

    // Configuration
    private readonly config = {
        debounceDelay: 300,
        minSearchLength: 1,
        searchFields: ['uid','title', 'category', 'description'],
        selectors: {
            searchInput: '.js-map-search-input',
            clearButton: '.js-map-search-clear',
            searchContainer: '.js-map-search-container',
        },
        classes: {
            hasValue: 'has-value',
            searching: 'is-searching',
            hasResults: 'has-results',
            noResults: 'no-results',
        },
    };

    constructor(mapSelector: string | StratisMap, options: Partial<typeof StratisMapFilter.prototype.config> = {}) {
        // Merge configuration
        this.config = { ...this.config, ...options };

        // Create debounced search function
        this.debouncedSearch = debounce.call(this, this.performSearch.bind(this), this.config.debounceDelay);

        // Initialize when map is ready
        if (typeof mapSelector === 'string') {
            this.waitForMap();
        } else {
            this.mapInstance = mapSelector;
            this.init();
        }
    }

    /**
     * Wait for map instance to be available
     */
    private waitForMap(): void {
    const checkMap = (): void => {
        if (window.db && window.db.StratisMap && window.db.StratisMap.length > 0) {
            [this.mapInstance] = window.db.StratisMap;
            this.init();
        } else {
            setTimeout(checkMap, 100);
        }
    };
    checkMap();
}

    /**
     * Initialize the filter component
     */
    private init(): void {
        if (this.isInitialized || !this.mapInstance) {
            return;
        }

        this.findElements();
        this.bindEvents();
        this.isInitialized = true;
    }

    /**
     * Find DOM elements
     */
    private findElements(): void {
        this.searchInput = document.querySelector(this.config.selectors.searchInput);
        this.clearButton = document.querySelector(this.config.selectors.clearButton);
        this.searchContainer = document.querySelector(this.config.selectors.searchContainer);

        if (!this.searchInput) {
            console.warn('StratisMapFilter: Search input not found');
        }
    }

    /**
     * Bind event listeners
     */
    private bindEvents(): void {
        if (!this.searchInput) {
            return;
        }

        // Search input events
        this.searchInput.addEventListener('input', this.handleSearchInput.bind(this));
        this.searchInput.addEventListener('keydown', this.handleKeyDown.bind(this));
        this.searchInput.addEventListener('focus', this.handleFocus.bind(this));
        this.searchInput.addEventListener('blur', this.handleBlur.bind(this));

        // Clear button event
        if (this.clearButton) {
            this.clearButton.addEventListener('click', this.clearSearch.bind(this));
        }

        // Map ready event
        document.addEventListener('mapReady', this.handleMapReady.bind(this));
    }

    /**
     * Handle search input changes
     */
    private handleSearchInput(event: Event): void {
        const target = event.target as HTMLInputElement;
        const value = target.value.trim();

        this.updateSearchState(value);
        this.debouncedSearch(value);
    }

    /**
     * Handle keyboard events
     */
    private handleKeyDown(event: KeyboardEvent): void {
        if (event.key === 'Escape') {
            this.clearSearch();
            event.preventDefault();
        }
    }

    /**
     * Handle input focus
     */
    private handleFocus(): void {
        if (this.searchContainer) {
            addClass(this.searchContainer, 'is-focused');
        }
    }

    /**
     * Handle input blur
     */
    private handleBlur(): void {
        if (this.searchContainer) {
            removeClass(this.searchContainer, 'is-focused');
        }
    }

    /**
     * Handle map ready event
     */
    private handleMapReady(): void {
        // Map is ready, we can now perform searches
        if (this.searchInput && this.searchInput.value.trim()) {
            this.performSearch(this.searchInput.value.trim());
        }
    }

    /**
     * Update search state classes
     */
    private updateSearchState(value: string): void {
        if (!this.searchContainer) {
            return;
        }

        if (value.length > 0) {
            addClass(this.searchContainer, this.config.classes.hasValue);
        } else {
            removeClass(this.searchContainer, this.config.classes.hasValue);
        }
    }

    /**
     * Perform the actual search
     */
    private performSearch(searchTerm: string): void {
        if (!this.mapInstance) {
            return;
        }

        if (searchTerm.length < this.config.minSearchLength) {
            this.mapInstance.resetFilters();
            return;
        }

        // Add searching state
        if (this.searchContainer) {
            addClass(this.searchContainer, this.config.classes.searching);
        }

        // Perform the filter
        this.mapInstance.filterMarkers(searchTerm, this.config.searchFields);

        // Remove searching state
        setTimeout(() => {
            if (this.searchContainer) {
                removeClass(this.searchContainer, this.config.classes.searching);
            }
        }, 100);
    }

    /**
     * Clear the search
     */
    public clearSearch(): void {
        if (this.searchInput) {
            this.searchInput.value = '';
            this.searchInput.focus();
        }

        if (this.mapInstance) {
            this.mapInstance.resetFilters();
        }

        this.updateSearchState('');
    }

    /**
     * Set search term programmatically
     */
    public setSearchTerm(term: string): void {
        if (this.searchInput) {
            this.searchInput.value = term;
            this.updateSearchState(term);
            this.performSearch(term);
        }
    }

    /**
     * Get current search term
     */
    public getSearchTerm(): string {
        return this.searchInput?.value.trim() || '';
    }

    /**
     * Destroy the filter component
     */
    public destroy(): void {
        if (this.searchInput) {
            this.searchInput.removeEventListener('input', this.handleSearchInput.bind(this));
            this.searchInput.removeEventListener('keydown', this.handleKeyDown.bind(this));
            this.searchInput.removeEventListener('focus', this.handleFocus.bind(this));
            this.searchInput.removeEventListener('blur', this.handleBlur.bind(this));
        }

        if (this.clearButton) {
            this.clearButton.removeEventListener('click', this.clearSearch.bind(this));
        }

        document.removeEventListener('mapReady', this.handleMapReady.bind(this));

        this.mapInstance = null;
        this.searchInput = null;
        this.clearButton = null;
        this.searchContainer = null;
        this.isInitialized = false;
    }
}
