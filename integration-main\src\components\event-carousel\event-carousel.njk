{% from Helpers.core import carousel with context %}

{% set params = Helpers.merge({
    wrapperClassName: 'event-carousel',
    itemsToShow: [4, 2, 1],
    actions: false,
    count: 6,
    item: 'event-item'
}, settings or {}) %}

{% call carousel.setCarousel(settings = {
    wrapperClassName: params.wrapperClassName,
    itemsToShow: params.itemsToShow,
    actions: params.actions
}) %}
    {% for item in range(params.count) %}
        <div class="event-carousel__item swiper-item">
            {{ getComponent(params.item) }}
        </div>
    {% endfor %}
{% endcall %}
