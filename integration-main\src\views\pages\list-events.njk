{%- import 'components/heading/heading.njk' as Heading -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- import 'views/core-components/filter.njk' as Filter -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'components/page-list/page-list.njk' import PageList -%}
{%- from 'components/events/events.njk' import EventsList -%}
{%- from 'components/popup/popup.njk' import Popup -%}
{%- from 'views/partials/search-sidebar-event.njk' import SearchSidebarEvent -%}
{%- from 'components/map/map.njk' import MapDetailPageList -%}


{% set count = 4 %}

{% set pageConfig = {
    category: 'Agenda',
    title: 'agenda',
    pageImage: false,
    hasFilter: false,
    bundle: 'list',
    useSidebarBeforeContent: true,
    bodyClass: 'is-list-with-filter',
    preloadCSS: [
        Helpers.path.css + '/map.css'
    ]
} %}

{% extends 'views/layouts/base.njk' %}

{% block scriptsFooter %}
    {{ super() }}
    <script src="{{ getScriptPath('map.bundle') }}" defer></script>
{% endblock %}

{% block heading %}
    {% call Heading.Heading(overwriteWrapper = true) %}
        {% call Heading.HeadingContent() %}
            {{ Heading.HeadingPartials('titleWithCategoryAndFilterResult', config.title, false, config.category) }}
        {% endcall %}
    {% endcall %}
{% endblock %}


{% block content %}
<div id="tx-solr-search" class="list-content">
    <div class="list-content__map">
        {{MapDetailPageList()}}
    </div>
    {% call PageList(
        useFilterButton = false,
        numberOfFilters = 5,
        useButtons = true,
        itemsCount = count,
        list = EventsList(tag = 'h3', cols = 4, listClass = 'is-list', cols = 1, count = count),
        pagerType = 'default',
        hasFilterResult = false
    ) %}
    {% endcall %}
</div>
{% endblock %}

{% block sidebarBefore %}
    <div class="list-content__sidebar-container">
        <nav role="navigation" class="site-content__sidebar {{ config.sidebarClass }}" aria-label="Filtre de recherche">
            {{ SearchSidebarEvent() }}
        </nav>
        {{ Link(
            className = 'btn is-small list-content__map-event',
            text = 'Proposer un événement',
            href = './page-proposer.html',
            icon = 'far fa-calendar-edit'
        ) }}
    </div>
{% endblock %}


{% block contentBottom %}{% endblock %}
