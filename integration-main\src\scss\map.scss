// In future case imports will be replaced with sass modules.
// import main configuration
@import 'utils/functions';
@import 'utils/config';

// Import utilities
@import 'utils/functions-helpers';
@import 'utils/mixins';
@import 'utils/breakpoints';
@import 'utils/flexbox-grid';
@import 'utils/placeholders';
@import 'utils/helpers';
@import 'utils/animations';
@import 'utils/revenge';

// Import fontawesome
/* stylelint-disable */
@import url('ol/ol.css');
//@import 'fontawesome/brands';
//@import 'fontawesome/light';
//@import 'fontawesome/regular';
@import 'fontawesome/fontawesome';
/* stylelint-enable */
@import 'core-components/button';
@import '../components/map/scss/map-localiser';
@import '../components/map-elements/map-home-link';
@import '../components/map-elements/map-list-link';
//@import '../components/map-elements/map-layer-switcher';
@import '../components/map-elements/map-hint';
@import '../components/map-elements/map-user-position';
@import '../components/map-elements/map-maps-switcher';
@import '../components/map-elements/map-zoom';
@import '../components/map-elements/map-site-domen';
@import '../components/map-elements/map-search-results-bottom';
@import '../components/map-template/map-template';
@import '../components/map-template/map-template-heading';
@import '../components/map-elements/map-search-results';
@import '../components/map-header/map-header';
@import '../components/map/scss/map';
@import '../components/map-elements/map-popup';
