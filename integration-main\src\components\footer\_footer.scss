// Footer styles

.footer {
    &__wrapper {
        background-color: var(--color-1--1);
        overflow: hidden;
        position: relative;
    }

    &__container {
        @extend %container;
        padding-bottom: 47px;
        padding-top: 49px;

        @include breakpoint(medium down) {
            padding: 48px 62px 128px;
        }

        @include breakpoint(small down) {
            padding: 42px 0 69px;
        }
    }

    &__row {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        @include breakpoint(medium down) {
            flex-wrap: wrap;
        }

        @include breakpoint(small down) {
            display: block;
        }
    }

    &__column {
        @include breakpoint(medium down) {
            margin: 0 auto;
            max-width: 768px;
        }

        @include breakpoint(small down) {
            max-width: 320px;
        }

        &.is-first {
            flex-grow: 1;
            width: 1%;

            @include breakpoint(medium down) {
                padding: 0 10px;
                width: 100%;
            }
        }

        &.is-second {
            padding: 15px 0;
            position: relative;
            width: 100%;

            @include breakpoint(medium down) {
                padding: 20px 0;
            }
        }
    }
}
