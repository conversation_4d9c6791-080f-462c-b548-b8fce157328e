.demarches {
    background-color: $color-3--1;
    margin: 0;
    padding: 60px 0 150px;

    @include breakpoint(medium down) {
        padding: 50px 0 65px;
    }

    &__container {
        @extend %container;

        @include breakpoint(medium only) {
            max-width: 1130px;
            padding-left: 50px;
            padding-right: 50px;
        }
    }

    & &__title {
        margin-bottom: 45px;
    }

    &__sub-title {
        margin-bottom: 30px;

        @include breakpoint(medium down) {
            &.is-secondary {
                .title__content {
                    justify-content: center;
                }

                .title__text::after {
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
    }
}
