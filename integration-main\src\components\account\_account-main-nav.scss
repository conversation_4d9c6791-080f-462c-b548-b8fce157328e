.account-main-nav {
    $this: &;

    display: flex;
    flex-wrap: wrap;
    margin-bottom: 130px;

    @include breakpoint(medium down) {
        margin-bottom: 80px;
    }

    &__container {
        @extend %container;
        max-width: 1140px;

        @include breakpoint(medium only) {
            padding: 0 52px;
        }
    }

    &__list {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        @include breakpoint(medium down) {
            justify-content: center;
        }

        @include breakpoint(small down) {
            box-shadow: 0 3px 6px rgba($color-black, 0.16);
        }
    }

    &__item {
        flex-shrink: 0;
        margin: 0 10px 20px;
        min-width: 200px;
        width: calc(25% - 20px);

        @include breakpoint(medium down) {
            width: calc(33.3333% - 20px);
        }

        @include breakpoint(small down) {
            margin: 0;
            width: 100%;
        }
    }

    &__link {
        --amn-item-bg-color: $color-white;
        --amn-item-color: var(--color-1--1);

        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        @include size(245px);
        @include trs();
        align-items: center;
        background-color: var(--amn-item-bg-color);
        box-shadow: 0 3px 6px rgba($color-black, 0.16);
        color: var(--amn-item-color);
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        justify-content: center;
        margin: 0 auto;
        text-align: center;
        text-decoration: none;

        @include breakpoint(medium down) {
            @include size(200px);
        }

        @include breakpoint(small down) {
            @include font(null, 1.6rem, var(--fw-normal));
            @include size(100%, auto);
            box-shadow: none;
            flex-direction: row;
            justify-content: flex-start;
            line-height: 1.8rem;
            padding: 12px 31px;
        }

        &.is-active,
        &:hover,
        &:focus {
            --amn-item-bg-color: var(--color-1--1);
            --amn-item-color: #{$color-white};
            box-shadow: none;

            svg {
                --amn-item-svg-color: #{$color-white};
            }
        }
    }

    &__item-icon,
    &__item-text {
        pointer-events: none;
    }

    &__item-icon {
        @include size(66px);
        margin: 0 auto 16px;

        @include breakpoint(small down) {
            @include size(24px);
            margin: 0 25px 0 0;
        }

        svg {
            --amn-item-svg-color: var(--color-1--1);

            @include trs(fill);
            @include size(100%);
            display: block;
            fill: var(--amn-item-svg-color);
            font-weight: var(--fw-normal);
        }
    }

    &.is-small {
        margin-bottom: 0;

        #{$this}__container {
            @include breakpoint(large only) {
                max-width: 1280px;
            }

            @include breakpoint(medium only) {
                padding: 0 62px;
            }
        }

        #{$this}__item {
            background-color: $color-white;
            margin: 0;
            min-width: initial;
            position: relative;
            width: auto;

            @include breakpoint(medium down) {
                flex-grow: 1;
            }

            @include breakpoint(small down) {
                width: 100%;
            }

            &:first-of-type {
                @include breakpoint(medium up) {
                    flex-grow: 1;
                    max-width: 63px;
                    min-width: 63px;
                    width: 1%;

                    #{$this}__link {
                        @include hide-text;
                        padding: 19px 10px;
                    }

                    #{$this}__item-icon {
                        margin: 0;
                    }
                }
            }
        }

        #{$this}__link {
            @include size(auto);
            @include font(var(--typo-1), 1.3rem, var(--fw-normal));
            box-shadow: 0 3px 6px rgba($color-black, 0.16);
            flex-direction: row;
            padding: 19px 25.9px;

            @include breakpoint(small down) {
                box-shadow: none;
                font-size: 1.5rem;
                padding: 12px 30px;
            }

            &.is-active,
            &:hover,
            &:focus {
                box-shadow: 0 3px 6px rgba($color-black, 0.16);

                @include breakpoint(small down) {
                    box-shadow: none;
                }
            }
        }

        #{$this}__item-icon {
            @include size(25px);
            margin: 0 11px 0 0;

            @include breakpoint(small down) {
                margin-right: 27px;
            }
        }
    }
}
