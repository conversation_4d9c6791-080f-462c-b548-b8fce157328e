.search-filter {
    $this: &;

    @include breakpoint(medium down) {
        padding-bottom: 40px;
    }

    &__toggle {
        @extend %button;
        @extend %button-style-primary;
        display: none;
        margin: 0 auto 20px;
        max-width: 450px;

        @include breakpoint(medium down) {
            display: flex;
        }

        &.is-open {
            #{$this}__toggle-icon::before {
                content: fa-content($fa-var-times);
            }
        }
    }

    &__toggle-icon {
        @include icon-before($fa-var-filter);
        margin-right: 5px;
        width: 18px;
    }

    &__wrapper {
        @include breakpoint(large) {
            display: block !important;
        }

        @include breakpoint(medium down) {
            display: none;
        }
    }
}

.search-aside {
    $this: &;

    &__group-wrapper:not(:last-child),
    &__group {
        margin-bottom: 60px;
    }

    &__group-title {
        @include font(var(--typo-1), 2rem, var(--fw-bold));
        color: $color-black;
        display: block;
        margin-bottom: 15px;
        text-transform: uppercase;
    }

    &__group-wrapper {
        margin-top: 22px;

        .is-commune {
            margin-bottom: 20px;
        }

        .form {
            &__legend {
                display: none;
            }

            &__field {
                min-height: 52px;
            }

            &__field-wrapper {
                margin-bottom: 0;
                
                > .form__label {
                    margin-bottom: 0;
                }
            }

            &__label {
                @include font(var(--typo-1), 2rem, var(--fw-bold));
                color: $color-black;
                display: block;
            }
        }

        .filter {
            &__fields-group {
                display: flex;
                flex-direction: column;
                margin-left: 0;
                margin-right: 43px;
                margin-top: 14px;
                max-width: 300px;
    
                @include breakpoint(small down) {
                    margin-right: 0;
                    margin-top: 15px;
                }
            }

            &__field {
                padding: 0;
            }

            &__buttons {
                justify-content: flex-start;

                @include breakpoint(small down) {
                    justify-content: center;
                }
            }

            input,
            select {
                background-color: transparent;
                background-position: right 0 top 50%;
                border: 0;
                border-bottom: 1px solid $color-black;
                color: $color-black;
                min-height: 43px;
                padding: 0;

                &::placeholder {
                    color: $color-black;
                }

                &:focus-visible {
                    border-bottom: 1px solid $color-black;
                    outline: none;
                }
            }
        }
    }

    &__group-thematique {
        margin-top: 31px;

        @include breakpoint(small down) {
            margin-top: 24px;
        }

        &-title {
            @include font(var(--typo-1), 2rem, var(--fw-bold));
            color: $color-black;
            display: block;
            margin-bottom: 10px;
        }

        &-title-more {
            cursor: pointer;
        }

        .form__field-wrapper {
            margin-bottom: 0;
        }

        .multiselect__toggle {
            @include size(100%,52px);
            background-color: transparent;
            border: 0;
            color: $color-black;
            padding: 0;

            &::after {
                color: $color-black !important;
            }

            &::placeholder {
                color: $color-black;
            }

            &:focus-visible {
                border-bottom: 1px solid $color-black;
                outline: none;
            }
        }
    }

    &__group-of-extreme-search {
        display: flex;
        flex-direction: column;
        gap: 18px;

        @include breakpoint(small down) {
            gap: 27px;
        }

        .period-date {
            margin-bottom: 0;

            .form__label {
                font-size: 1.6rem;
                margin-bottom: 4px;
            }

            .form__date-range {
                display: flex;
                flex-direction: row;
                gap: 7px;

                @include breakpoint(medium down) {
                    gap: 0;
                }

                @include breakpoint(small down) {
                    gap: 8px;
                }

                .form__date-field {
                    align-items: center;
                    display: flex;
                    flex-direction: row;

                    .form__date-label {
                        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
                        color: $color-black;
                        display: block;
                        margin-right: 10px;
                    }

                    input {
                        @include size(87px,33px);
                        @include min-size(87px,33px);
                        background-color: $color-white;
                        border: 1px solid $color-4--2;
                        cursor: pointer;
                        padding: 0;
                    }

                    .form__date-icon {
                        left: -20px;
                        pointer-events: none;
                        position: relative;
                    }
                }
            }
        }
    }

    &__select {
        &[class] {
            display: flex;
            flex-wrap: wrap;
            gap: 16px 18px;
        }

        &-item {
            label {
                @include font(var(--typo-1), 1.3rem, var(--fw-medium));
                @include size(fit-content,28px);
                align-items: center;
                background-color: $color-white;
                border: 1px solid var(--color-1--1);
                color: var(--color-1--1);
                cursor: pointer;
                display: flex;
                justify-content: center;
                padding: 0 12px;
            }
        }
    }
}

.search-facets {
    $this: &;

    &__wrap {
        margin-bottom: 30px;
    }

    &__title {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        border-bottom: 5px solid var(--color-1--1);
        color: $color-3--5;
        display: block;
        margin-bottom: 20px;
        padding: 0 10px 5px;
        position: relative;

        &::after {
            @include triangle('bottom', var(--color-1--1), 14px, 7px);
            @include absolute(100%, null, null, 10px);
            content: '';
            margin-top: 5px;
        }
    }

    &__content {
        padding: 0 20px;

        #tx-solr-facets-in-use & {
            padding: 0;
        }
    }

    &__actions {
        margin-top: 20px;
        width: 100%;
    }

    &__reset {
        @extend %button;
        @extend %button-size-small;
        max-width: 300px;
    }

    &__daterange-field {
        margin-bottom: 15px;
    }

    &__daterange-submit {
        @extend %button;
        @extend %button-style-primary;
        max-width: 300px;
    }
}

.search-no-results {
    align-items: center;
    display: flex;
    flex-direction: column;
    position: relative;

    @include fa-icon-style(false) {
        color: var(--color-2--1);
        font-size: 5rem;
        font-weight: var(--fw-bold);
        margin-bottom: 20px;
        pointer-events: none;
    }

    &__content {
        text-align: center;

        p {
            font-style: italic;
        }
    }
}

label.is-filter-active {
    background-color: var(--color-1--1);
    color: $color-white;
}

.is-hidden-filters {
    display: none;
}

.site-content__main {
    .is-list-with-filter & {
        @include breakpoint(small down) {
            width: 100%;
        }
    }
}
