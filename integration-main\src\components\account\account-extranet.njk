{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'components/account/account-box.njk' import AccountBoxItem -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/logo/logo.njk' import Logo -%}
{%- from 'views/core-components/icon.njk' import Icon -%}

{#
    CreateAccountForm template.
#}
{% macro CreateAccountForm() %}
    {%- call Form.FormWrapper(
        legend = false,
        className = 'account-extranet__form js-validator-form'
    ) -%}
        <fieldset class="form__fieldset">
            <legend class="form__legend legend is-background">Vos identifiants</legend>
            {% call Form.FormGroup() %}
                <div class="col-xs-12">
                    {%- call Form.FormField(
                        type = 'email',
                        label = 'Courriel',
                        required = true,
                        customAttrs = {
                            'autocomplete' : 'email',
                            'aria-describedby': 'input-help-1'
                        }
                    ) -%}
                        <p class="text-help" id="input-help-1" aria-hidden="true">Saisir une adresse électronique valide (ex. : <EMAIL>)</p>
                    {%- endcall -%}
                </div>
                <div class="col-xs-12">
                    {{ Form.SuperPassword() }}
                </div>
                <div class="col-xs-12">
                    {{ Form.FormField(
                        label = 'Confirmation du mot de passe',
                        type = 'password',
                        required = true,
                        enablePasswordVisibilityManager = true,
                        id = 'confirmation-password',
                        fieldClass = 'js-validator-identical',
                        customAttrs = {
                            'autocomplete' : 'new-password',
                            'data-compare-with-id': 'identical-field-password'
                        }
                    ) }}
                </div>
            {% endcall %}
        </fieldset>
        <fieldset class="form__fieldset">
            <legend class="form__legend legend is-background">Vos Coordonnées</legend>
            {% call Form.FormGroup() %}
                    <div class="col-lg-12 col-xs-12">
                        {{ Form.FormField(
                            type = 'select',
                            label = 'Civilité :',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'honorific-prefix'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-12 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Nom :',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'family-name'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-12 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Prénom :',
                            required = true,
                            customAttrs = {
                                'autocomplete' : 'given-name'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-12 col-xs-12">
                        {%- call Form.FormField(
                            type = 'tel',
                            label = 'Téléphone :',
                            customAttrs = {
                                'autocomplete' : 'tel',
                                'aria-describedby': 'input-help-2'
                            }
                        ) -%}
                            <p class="text-help" id="input-help-2" aria-hidden="true">10 chiffres avec ou sans espace, ex. : 01 23 45 67 89 ou 0123456789</p>
                        {%- endcall -%}
                    </div>
                    <div class="col-lg-12 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Adresse :',
                            customAttrs = {
                                'autocomplete' : 'street-address'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-4 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Code postal :',
                            customAttrs = {
                                'autocomplete' : 'postal-code',
                                'pattern': '^[0-9]*$',
                                'maxlength' : '5'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-8 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Ville de résidence :',
                            customAttrs = {
                                'autocomplete' : 'address-level2'
                            }
                        ) }}
                    </div>
                    <div class="col-lg-12 col-xs-12">
                        {{ Form.FormField(
                            type = 'text',
                            label = 'Pays :',
                            customAttrs = {
                                'autocomplete' : 'address-level2'
                            }
                        ) }}
                    </div>
                    <div class="col-xs-12">
                        {{ Form.RadioCheckbox(
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données.
                            <a href="javascript:;"
                                role="button"
                                aria-haspopup="dialog"
                                data-dialog-label="Page de politique de protection des données"
                                data-type="iframe"
                                data-fancybox
                                data-src="./test-form.html"
                                data-title="Page de politique de protection des données">
                                Cliquez ici pour les consulter.
                            </a>',
                            required = true,
                            disableRequiredLabel = false
                        ) }}
                    </div>
                {% endcall %}
        </fieldset>
        {% call Form.FormActions(className = 'is-center is-stretch is-margin-top') %}
            {{ Button(
                className = 'btn is-ternary',
                type = 'submit',
                icon = 'far fa-user-plus',
                text = 'Créer mon compte'
            ) }}
        {% endcall %}
    {%- endcall -%}
{% endmacro %}

{#
    LoginAccountForm template.
#}
{% macro LoginAccountForm() %}
    {% set loginLabel %}
        <span class="far fa-user-circle" aria-hidden="true"></span>
        <span class="ghost">Identifiant</span>
    {% endset %}

    {% set passwordLabel %}
        <span class="far fa-lock-alt" aria-hidden="true" title="Mot de passe"></span>
        <span class="ghost">Mot de passe</span>
    {% endset %}

    {%- call Form.FormWrapper(
        className = 'login-form',
        legend = '',
        legendTag = 'div',
        legendClassName = 'ghost'
    ) -%}
        <div class="flex-row">
            <div class="col-xs-12">
                {{ Form.FormField(
                    wrapperModifier = 'is-inline',
                    type = 'text',
                    label = loginLabel,
                    placeholder = 'Identifiant',
                    fieldClass = 'js-tooltip',
                    customAttrs = {
                        'data-content': 'Identifiant',
                        'autocomplete': 'username'
                    }
                ) }}
            </div>
            <div class="col-xs-12">
                {{ Form.FormField(
                    wrapperModifier = 'is-inline',
                    type = 'password',
                    label = passwordLabel,
                    fieldClass = 'js-tooltip',
                    placeholder = 'Mot de passe...',
                    customAttrs = {
                        'data-content': 'Mot de passe',
                        'autocomplete': 'current-password'
                    }
                ) }}
                <p class="login-form__helper-text">
                    {{ Link(
                        className = '',
                        href = '#',
                        icon = false,
                        text = 'Mot de passe perdu ? Réinitialisez-le !',
                        attrs = {
                            'data-src': './iframe-account-message.html',
                            'data-fancybox': 'message5',
                            'data-type': 'iframe',
                            'data-role': 'presentation',
                            'data-fancybox-transparent': 'true',
                            'data-dialog-label': 'Demande de réinitialisation du mot de passe',
                            'data-fancybox-center-content': 'true',
                            'data-fancybox-body-class': 'is-popup-opened',
                            'aria-label' : 'Mot de passe oublié ? Réinitialisez-le (fenêtre modale)',
                            'aria-haspopup' : 'dialog'
                        }
                    ) }}
                </p>
            </div>
            <div class="col-md-6 col-xs-12">
                {{ Form.RadioCheckbox(
                    label = 'Se souvenir de moi',
                    checked = true
                ) }}
            </div>
            <div class="col-md-6 col-xs-12">
                {% call Form.FormActions() %}
                    {{ Button(
                        className = 'btn is-ternary',
                        type = 'submit',
                        icon = 'far fa-sign-in',
                        text = 'Se connecter'
                    ) }}
                {% endcall %}
            </div>
        </div>
    {% endcall %}
{% endmacro %}

{#
    HaveAnAccount template.
#}
{%- macro HaveAnAccount(
    title = 'Vous avez déjà un compte ?',
    teaser = 'Accédez à votre compte en utilisant votre courriel et votre mot de passe. </br> <a href="#">Mot de passe perdu ? Réinitialisez-le !</a>',
    buttonText = 'Se connecter',
    buttonIcon = 'far fa-sign-in'
) -%}
    {% call Section(className = 'have-an-account', container = false) %}
        <div class="have-an-account__info">
            <h2 class="have-an-account__title">{{ title }}</h2>
            <p class="have-an-account__teaser">{{ teaser | safe }}</p>
        </div>
        {{ Link(
            className = 'btn is-inverted',
            text = buttonText,
            icon = buttonIcon
        ) }}
    {% endcall %}
{%- endmacro -%}

{#
    CreateAccount template.
#}
{%- macro CreateAccount(loginAccount = false, extranet = false) -%}
    <div class="account-extranet">
        {% if not extranet %}
            <section class="account-extranet__column">
                {{ AccountBoxItem(
                    titleText = 'Utilisez FranceConnect',
                    separator = true,
                    animation = not loginAccount
                ) }}
            </section>
        {% endif %}
        <section class="account-extranet__column">
            <div class="account-extranet__wrapper">
                {% if loginAccount %}
                    <div class="account-extranet__text">
                        <h2 class="account-extranet__title">Utilisez vos identifiants</h2>
                        {% if not extranet %}
                            <p  class="account-extranet__description">Accédez à votre compte en utilisant votre courriel et votre mot de passe.</p>
                        {% endif %}
                    </div>
                    {{ LoginAccountForm() }}
                {% else %}
                    <div class="account-extranet__text">
                        <h2 class="account-extranet__title">Créez un nouveau profil</h2>
                        <p  class="account-extranet__description">La création de votre compte vous permettra de pré-remplir automatiquement vos démarches et de les suivre dans votre tableau de bord.</p>
                    </div>
                    {{ CreateAccountForm() }}
                {% endif %}
            </div>
        </section>
    </div>
{%- endmacro -%}

{#
    AccountPageHeader template.
#}
{%- macro AccountPageHeader(
    description = false
) -%}
    <div class="account-page-header">
        <div class="account-page-header__info">
            <span class="account-page-header__icon far fa-user-circle" aria-hidden="true"></span>
            <div class="account-page-header__text">
                <h1 class="account-page-header__title">Mon Compte Utilisateur</h1>
                {%- if description -%}
                    <p class="account-page-header__description">{{ description | safe }}</p>
                {%- endif -%}
            </div>
        </div>
        <a href="#"class="account-page-header__right">
            <span><strong>cu-arras</strong>.fr</span>
            <div class="account-page-header__return">
                <span aria-hidden="true" class="far fa-arrow-from-right"></span>
                <span class="account-page-header__return-text">Retour au site</span>
            </div>
        </a>
    </div>
{%- endmacro -%}

{#
    AccountPageContainer template.
#}
{%- macro AccountPageContainer(
    className = '',
    useNavbar = false,
    activeNavbarIndex = 1
) -%}
    <div class="account-page {{ className }}">
        <div class="account-page-container">
            {%- if useNavbar -%}
                {{ AccountNavbar(active = activeNavbarIndex) }}
            {%- endif -%}
            <div class="account-page__content">
                {{ caller() if caller }}
            </div>
        </div>
    </div>
{%- endmacro -%}
