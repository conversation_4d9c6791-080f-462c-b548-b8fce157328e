{%- from 'views/utils/utils.njk' import svg -%}

{%- macro DropArea() -%}
    <div class="drop-area js-drop-area" data-name="fileList[]">
        <label for="drop-area-input" class="ghost">Télécharger des fichiers</label>
        <input
            id="drop-area-input"
            name="drop-area__name"
            type="file"
            multiple="true"
        >
        <div class="drop-area__block js-drop-area-block">
            <div class="drop-area__content">
                {{ svg('download', 50, 46.7) }}
                <h3 class="drop-area__title">Déposez vos fichiers</h3>
            </div>
        </div>
        <div class="form__files-list-wrapper js-file-list" aria-live="polite" data-related-field="drop-area-input"></div>
    </div>
{%- endmacro -%}


