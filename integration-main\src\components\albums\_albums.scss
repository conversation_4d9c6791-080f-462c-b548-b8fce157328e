.albums-home {
    $this: &;

    &.is-moved {
        .section__title {
            margin-bottom: 45px;

            @include breakpoint(medium down) {
                margin-bottom: 55px;
            }

            @include breakpoint(small down) {
                margin-bottom: 30px;
            }
        }

        .list__item {
            margin-bottom: 0;

            &:nth-child(even) {
                margin-top: -80px;

                @include breakpoint(medium down) {
                    margin-top: -45px;
                }

                @include breakpoint(small down) {
                    margin-top: 0;
                }
            }
        }
    }

    &__container {
        @extend %container;

        @include breakpoint(medium down) {
            padding: 0 62px;
        }

        @include breakpoint(small down) {
            padding: 0 30px;
        }

        &.is-fluid {
            @extend %container-fluid;

            @include breakpoint(small down) {
                padding: 0 30px;
            }
        }
    }

    .section {
        &__title {
            @extend %container;
            margin-bottom: 30px;
        }

        &__more-links {
            margin-top: 70px;

            @include breakpoint(medium down) {
                margin-top: 35px;
            }

            @include breakpoint(small down) {
                margin-top: 30px;
            }
        }
    }

    .list__item {
        margin-bottom: 10px;
    }
}
