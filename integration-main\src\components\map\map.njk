{% from 'views/utils/utils.njk' import setAttr, setAttributes %}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'components/map-elements/map-elements.njk' import MapPopup, LayerSwitcher, UserPosition, MapHint, ZoomControll, MapStructure, MapEquipment -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitleRTE -%}
{%- from 'components/events/events.njk' import MapEventItem -%}
{# Map template for localiser #}

{#
    Layer default settings.
    @prop {string} className - set custom class name for layer.
    @prop {string} uid - layer unique id.
    @prop {string} title - layer title.
    @prop {string} url - layer source url.
    @prop {string} type - layer type kml/geojson/etc.
    @prop {string} index - layer index.
    @prop {string} disableInteractions - disable layer interactions.
    @prop {string} style - set custom layer styles.
    @prop {object} attrs - layer custom attributes.
#}
{% set layerDefaults = {
    className: '',
    uid: '10101',
    title: 'Layer title',
    url: '',
    type: 'kml',
    index: '',
    disableInteractions: '0',
    style: 'default',
    styleProps: '{
        "route": {
            "color": "rgba(202, 0, 10, 1)",
            "width": 6
        }
    }',
    attrs: {}
} %}

{#
    Insert mapLayer
    @param {object} settings - add settings for map layer.
#}
{% macro MapLayer(settings = {}) %}
    {% set settings = Helpers.merge(layerDefaults, settings) %}
    <span
        class="js-map-layer {{ settings.className }}"
        {{ setAttr('data-uid', settings.uid) }}
        {{ setAttr('data-title', settings.title) }}
        {{ setAttr('data-url', settings.url) }}
        {{ setAttr('data-type', settings.type) }}
        {{ setAttr('data-index', settings.index) }}
        {{ setAttr('data-disable-interactions', settings.disableInteractions) }}
        {{ setAttr('data-style', settings.style) }}
        {{ setAttr('data-custom-style-props', settings.styleProps) }}
        {{ setAttributes(settings.attrs) }}
    >
        {{ caller() if caller }}
    </span>
{% endmacro %}

{#
    Marker default settings.
    @prop {string} className - set custom class name for marker.
    @prop {string} uid - marker unique id.
    @prop {string} lat - marker lat coordinate.
    @prop {string} lon - marker lon coordinate.
    @prop {string} icon - marker icon.
    @prop {string} iconSelected - marker selected icon.
    @prop {string} type - marker query type. Use it for rendering different layouts.
    @prop {object} attrs - marker custom attributes.
    @prop {string} bingMapsKey - add this string inside mapSettings {} if need satellite view for map:
           bingMapsKey: "AsrjxdrdKBh1V7kosotRsPEzajn_Mv3xUYu8HJLzzXeaHJMhQlnIsIeYuZu_UMwD",
#}
{% set markerDefaults = {
    className: '',
    uid: '1',
    lat: '48.373921',
    lon: '-4.568081',
    icon: Helpers.path.images + '/map/map-marker.svg',
    iconSelected: Helpers.path.images + '/map/map-marker.svg',
    type: '520',
    attrs: {}
} %}

{#
    Insert mapMarker.
    @param {object} settings - add settings for marker object.
#}
{% macro MapMarker(settings = {}) %}
    {% set settings = Helpers.merge(markerDefaults, settings) %}
    <span
        class="js-map-marker {{ settings.className }}"
        {{ setAttr('data-uid', settings.uid) }}
        {{ setAttr('data-lat', settings.lat) }}
        {{ setAttr('data-lon', settings.lon) }}
        {{ setAttr('data-icon', settings.icon) }}
        {{ setAttr('data-icon-selected', settings.iconSelected) }}
        {{ setAttr('data-type', settings.type) }}
        {{ setAttributes(settings.attrs) }}
    >
        {{ caller() if caller }}
    </span>
{% endmacro %}

{#
    Map default settings.
    @prop {number} pageId - set page id.
    @prop {boolean} useContext - set use context.
    @prop {boolean} useEvents - set useEvents setting for popup on map.
    @prop {boolean} useTitleLink - set useTitleLink setting for popup on map.
    @prop {object} mapSettings - set main settings for map.
    @prop {object} data - set main settings for layers and markers.
#}
{% set mapDefaultParams = {
    pageId: 100,
    useContext: true,
    useEvents: false,
    useTitleLink: false,
    useMapTools: true,
    isLocaliser: false,
    mapSettings: {
        canvasAriaLabel: "Carte interactive",
        zoomToExtent: "1",
        zoomToContentOnLoad: "1",
        zoomToExtentOnMarkerClick: "1",
        zoomToExtentOnClusterClick: "1",
        zoomToContentOnPopupClose: "1",
        showPopupForSingleMarker: "1",
        disableFocusOnPopup: "0",
        disableMarkerClick: "0",
        defaultMarker: Helpers.path.images + "/map/map-marker.svg",
        selectedMarker: Helpers.path.images + "/map/map-marker.svg",
        markerColor: '#17145c',
        selectedMarkerColor: '#26c3ec',
        defaultLat: "43.4803088918",
        defaultLon: "-1.460096987",
        defaultZoom: "13",
        minZoom: "0",
        maxZoom: "20",
        showZoomButtons: "1",
        zoomInTipLabel: "Zoom +",
        zoomOutTipLabel: "Zoom −",
        mouseWheelZoom: "1",
        OSMService: "",
        useGeoportal: "0",
        geoportalKey: "pratique",
        BERequestUrl: "./dynamic-map-popup.html?tx_news_pi1[overwriteDemand][idList]={uid}&type={type}"
    },
    data: {
        layers: [
            { uid: '13423', title: 'Layer 1', type: 'kml', url: 'media/doc.kml', disableInteractions: true },
            { uid: '243251', title: 'Layer 2', type: 'geojson', url: Helpers.path.js + '/data/w_arret_minute.json', disableInteractions: false },
            { uid: '12345', title: 'Layer 3', type: 'gpx', url: Helpers.path.js + '/data/test.gpx', disableInteractions: false }
        ],
        markers: [
            { uid: '10', lon: '1.526984786', lat: '43.32591517', attrs: { 'data-url': './index.html'} },
            { uid: '1', lon: '-1.479984786', lat: '43.47791517', html: 'Marker message', attrs: {  'data-title': 'Non reprehenderit cillum est ullamco' } },
            { uid: '2', lon: '-1.526984786', lat: '45.32591517', html: 'Marker message 2', attrs: {  'data-title': 'Lorem occaecat nostrud in do' } },
            { uid: '3', lon: '2.526984786', lat: '43.32591517', html: 'Marker message 3' },
            { uid: '4', lon: '2.526984786', lat: '33.32591517', html: 'Marker message 4' },
            { uid: '5', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 5' },
            { uid: '6', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 6' },
            { uid: '7', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 7' },
            { uid: '8', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 8' },
            { uid: '9', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 9' }
        ]
    }
}%}

{#
    Map template.
    @param {object} settings - add settings for map.
#}
{% macro Map(settings = {}) %}
    {% set params = Helpers.merge(mapDefaultParams, settings) %}
    {% set uid = Helpers.unique() %}

    {%- if params.useEvents -%}
        {% set params = Helpers.merge(params, {
            mapSettings: {
                BERequestUrl: "./dynamic-map-popup-agenda.html?tx_news_pi1[overwriteDemand][idList]={uid}&type={type}"
            }
        }) %}
    {%- endif -%}

    <div class="map {{ 'js-map-context' if params.useContext }}">
        <div class="map__container">
            <div
                class="map__element js-map"
                data-page-id="{{ params.pageId }}"
                data-map-options='{{ Helpers.stringify(params.mapSettings) }}'
            >
                {%- if not params.isLocaliser -%}
                    <h2 class="ghost">Cartographie des résultats</h2>
                {%- endif -%}
                <div class="map__tools" role="list" aria-roledescription="liste des boutons de contrôle de la carte">
                    {{ MapHint(isLocaliser = settings.isLocaliser) }}
                    {%- if params.useMapTools -%}
                        {# uncomment for use switcher to the satellite view  #}
{#                        {{ LayerSwitcher() }}#}
                        {{ UserPosition() }}
                    {%- endif -%}
                    {{ ZoomControll(isLocaliser = settings.isLocaliser) }}
                </div>
                <div class="map__data" hidden>
                    {% for layer in params.data.layers %}
                        {{ MapLayer(layer) }}
                    {% endfor %}

                    {% for marker in params.data.markers %}
                        {% call MapMarker(marker) %}
                            {{ marker.html | safe }}
                        {% endcall %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {#
            Add is-hidden class to hide popup.
        #}
        <div class="map__popup js-map-popup-context is-hidden"
            {{ setAttr('aria-modal', 'true') if not params.isLocaliser }}
            {{ setAttr('role', 'dialog') if not params.isLocaliser }}>
            {{ MapPopup({
                useEvents: params.useEvents,
                useTitleLink: params.useTitleLink
            }) }}
        </div>
    </div>
{% endmacro %}

{#
    Localiser default settings.
    @prop {string} modifier - add modifier to block.
    @prop {string} button - set button text.
    @prop {boolean} useTitleLink - set useTitleLink setting for popup on map.
    @prop {object} mapSettings - set settings for map.
    @prop {object} data - set data for map.
#}
{% set defaultLocaliserParams = {
    modifier: '',
    button: 'Localiser',
    useTitleLink: false,
    useMapTools: false,
    isLocaliser: true,
    mapSettings: {
        mouseWheelZoom: '0'
    },
    data: {}
}%}

{#
    MapLocaliser template.
    @param {object} settings - add settings for map.
#}
{% macro MapLocaliser(settings = {}) %}
    {% set params = Helpers.merge(defaultLocaliserParams, settings) %}

    <div class="map-localiser {{ params.modifier }}">
        {% set uid = 'localiser-' + Helpers.unique() %}
        <h2 class="map-localiser__button-wrapper has-mb-2">
            {{ Button(
                className = 'btn is-large is-ternary map-localiser__button js-localiser-toggle',
                icon = 'far fa-map-marker-alt',
                text = params.button,
                attrs = {
                    'data-sd-id': uid
                }
            ) }}
        </h2>
        <div class="map-localiser__wrapper" data-sd-content="{{ uid }}">
            {{ Map({
                mapSettings: params.mapSettings,
                data: params.data,
                useTitleLink: params.useTitleLink,
                useMapTools: params.useMapTools,
                isLocaliser: params.isLocaliser
            }) }}
        </div>
    </div>
{% endmacro %}

{#
    MapLocaliserContent template.
#}
{% macro MapLocaliserContent(modifier = '', tag ='h2') %}
    {% call Section(
        className = 'js-map-title-handler localiser-content ' + modifier,
        container = false,
        attrs = {
            'role' : 'region',
            'aria-label' : 'carte interactive'
        }
    ) %}
        <div class="section__title">
            {{ TitleRTE(
                text = 'Situer',
                iconPath = false,
                tag = tag
            ) }}
        </div>
        <div class="section__content">
            {{ MapLocaliser({
                button: 'Localiser',
                useTitleLink: true
            }) }}
        </div>
    {% endcall %}
{% endmacro %}

{#
    MapLocaliserManualContent template.
#}
{% macro MapLocaliserManualContent() %}
    {% call Section(className = 'localiser-content', container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = 'Situer manual',
                iconPath = false
            ) }}
        </div>
        <div class="section__content">
            {{ MapLocaliser({
                button: 'Localiser manual',
                useTitleLink: true,
                data: {
                    layers: [],
                    markers: [
                        { uid: '1', lon: '-1.479984786', lat: '43.47791517', html: 'Marker message', attrs: { 'data-manual-point': true } }
                    ]
                }
            }) }}
        </div>
    {% endcall %}
{% endmacro %}

{#
    Map for single-lists template.
#}

{% macro MapDetailPage(
    showPopupForSingleMarker = "1",
    disableMarkerClick = "0",
    disableFocusOnPopup = "1"
    ) %}
    {% call Section(
        className = 'map-detail',
        container = false,
        tag = 'div',
        attrs = {
            'role' : 'region',
            'aria-label' : 'carte interactive'
        }
    ) %}
        {{ Map({
            useMapTools: false,
            isLocaliser: true,
            mapSettings: {
                showZoomButtons: "0",
                showPopupForSingleMarker: showPopupForSingleMarker,
                disableMarkerClick: disableMarkerClick,
                disableFocusOnPopup: disableFocusOnPopup
            },
            data: {
                layers: false,
                markers:[{ uid: '13', lon: '2.526984786', lat: '43.32591517', html: 'Marker message 13' }]
            }
        }) }}
    {% endcall %}
{% endmacro %}

{% macro MapDetailPageList() %}
    {% call Section(
        className = 'map-detail',
        container = false,
        tag = 'div',
        attrs = {
            'role' : 'region',
            'aria-label' : 'carte interactive'
        }
    ) %}
        <div class="map-cluster-elements">
            <div class="map-cluster-elements__close-icon">
                {% call Button(
                    className = 'map-template-heading__close',
                    icon = 'far fa-times'
                    ) %}
                    <span class="ghost">Fermer les résulats</span>
                {% endcall %}
            </div>
            <div class="map-cluster-elements__content">
                <ul class="map-cluster-elements__list">
                {% for item in range(10) %}
                    <li class="map-cluster-elements__item">
                        {{ MapEventItem() }}
                    </li>
                {% endfor %}
                </ul>
            </div>
        </div>
        {{ Map() }}
    {% endcall %}
{% endmacro %}

{% macro MapDetailPageListStructure(
    showPopupForSingleMarker = "1",
    disableMarkerClick = "0",
    disableFocusOnPopup = "1"
    ) %}
    {% call Section(
        className = 'map-detail',
        container = false,
        tag = 'div',
        attrs = {
            'role' : 'region',
            'aria-label' : 'carte interactive'
        }
    ) %}
        <div class="map-cluster-elements">
            <div class="map-cluster-elements__close-icon is-structure">
                {% call Button(
                    className = 'btn is-small is-only-icon is-secondary map-popup__close map-template-heading__close',
                    tooltip = 'Fermer cette fiche',
                    icon = 'far fa-times'
                ) %}
            <span class="ghost">Fermer cette fiche</span>
        {% endcall %}
            </div>
            <div class="map-cluster-elements__content">
                <ul class="map-cluster-elements__list is-structure">
                {% for item in range(10) %}
                    <li class="map-cluster-elements__item">
                        {{ MapStructure() }}
                    </li>
                {% endfor %}
                </ul>
            </div>
        </div>
        {{ Map({
            useMapTools: false,
            isLocaliser: true,
            mapSettings: {
                showZoomButtons: "0",
                showPopupForSingleMarker: showPopupForSingleMarker,
                disableMarkerClick: disableMarkerClick,
                disableFocusOnPopup: disableFocusOnPopup
            },
            data: {
                layers: false,
                markers: [
                    { uid: '10', lon: '1.526984786', lat: '43.32591517', html: 'Marker message 99' },
                    { uid: '1', lon: '-1.479984786', lat: '43.47791517', html: 'Marker message' },
                    { uid: '2', lon: '-1.526984786', lat: '45.32591517', html: 'Marker message 2' },
                    { uid: '3', lon: '2.526984786', lat: '43.32591517', html: 'Marker message 3' },
                    { uid: '4', lon: '2.526984786', lat: '33.32591517', html: 'Marker message 4' },
                    { uid: '5', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 5' },
                    { uid: '6', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 6' },
                    { uid: '7', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 7' },
                    { uid: '8', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 8' },
                    { uid: '9', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 9' },
                    { uid: '10', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 10' },
                    { uid: '11', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 11' },
                    { uid: '12', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 12' },
                    { uid: '13', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 13' }
                ]
            }
        }) }}
    {% endcall %}
{% endmacro %}

{% macro MapDetailPageListEquipments(
    showPopupForSingleMarker = "1",
    disableMarkerClick = "0",
    disableFocusOnPopup = "1",
    name = false,
    email = false,
    commune = false,
    address = true
    ) %}
    {% call Section(
        className = 'map-detail',
        container = false,
        tag = 'div',
        attrs = {
            'role' : 'region',
            'aria-label' : 'carte interactive'
        }
    ) %}
        <div class="map-cluster-elements">
            <div class="map-cluster-elements__close-icon is-structure">
                {% call Button(
                    className = 'btn is-small is-only-icon is-secondary map-popup__close map-template-heading__close',
                    tooltip = 'Fermer cette fiche',
                    icon = 'far fa-times'
                ) %}
            <span class="ghost">Fermer cette fiche</span>
        {% endcall %}
            </div>
            <div class="map-cluster-elements__content">
                <ul class="map-cluster-elements__list is-structure">
                {% for item in range(10) %}
                    <li class="map-cluster-elements__item">
                        {{ MapEquipment(email = email, commune = commune, name = name, address = address) }}
                    </li>
                {% endfor %}
                </ul>
            </div>
        </div>
        {{ Map({
            useMapTools: false,
            isLocaliser: true,
            mapSettings: {
                showZoomButtons: "0",
                showPopupForSingleMarker: showPopupForSingleMarker,
                disableMarkerClick: disableMarkerClick,
                disableFocusOnPopup: disableFocusOnPopup
            },
            data: {
                layers: false,
                markers: [
                    { uid: '10', lon: '1.526984786', lat: '43.32591517', html: 'Marker message 99' },
                    { uid: '1', lon: '-1.479984786', lat: '43.47791517', html: 'Marker message' },
                    { uid: '2', lon: '-1.526984786', lat: '45.32591517', html: 'Marker message 2' },
                    { uid: '3', lon: '2.526984786', lat: '43.32591517', html: 'Marker message 3' },
                    { uid: '4', lon: '2.526984786', lat: '33.32591517', html: 'Marker message 4' },
                    { uid: '5', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 5' },
                    { uid: '6', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 6' },
                    { uid: '7', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 7' },
                    { uid: '8', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 8' },
                    { uid: '9', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 9' },
                    { uid: '10', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 10' },
                    { uid: '11', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 11' },
                    { uid: '12', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 12' },
                    { uid: '13', lon: '2.526984786', lat: '33.32591520', html: 'Marker message 13' }
                ]
            }
        }) }}
    {% endcall %}
{% endmacro %}

