{%- from 'views/core-components/infos.njk' import InfoBlock, InfoItem, CreateInfoItem -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/widget.njk' import Widget -%}
{%- from 'views/core-components/title.njk' import TitleRTE, TitleWidget -%}
{%- from 'components/social/social.njk' import SocialMenu -%}

{#
    ContactItem template.
#}
{%- macro ContactsItem(
    className = '',
    imageSizes = ['200x200?1279', '250x250'],
    category = true,
    contentInfo = true,
    structure = false
) -%}
    <article class="contact-item {{ 'is-structure' if structure }} {{ className }}">
        {% if structure %}
        {{ Image({
            sizes: imageSizes,
            className: 'contact-item__picture',
            serviceID: range(50) | random,
            alt: ''
        }) }}
        {% else %}
        {{ Image({
            className: 'contact-item__picture avatar-image',
            image: ['organigramme-placeholder.svg'],
            alt: ''
        }) }}
        {% endif %}
        <div class="contact-item__content">
            <div class="contact-item__content-top">
                <h3 class="contact-item__title">
                    {%- if category and structure %}
                        <span class="contact-item__theme">Thématique</span>
                        <span class="sr-only"> : </span>
                    {%- endif %}
                    <a href="single-{{ 'structures' if structure else 'contacts' }}.html" class="contact-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor' if structure else 'Mme Prénomlorem Nomsitamet' }}
                        </span>
                    </a>
                </h3>
            </div>
            {% if contentInfo %}
                <div class="contact-item__content-info">
                {% if structure %}
                    <div class="contact-item__details">
                            {% call InfoBlock() %}
                                {{ InfoItem(type = 'address') }}
                                {{ InfoItem(type = 'hours') }}
                            {% endcall %}
                            {% call InfoBlock() %}
                                {{ InfoItem(type = 'website') }}
                            {% endcall %}
                            <p class="contact-item__function is-main">Fonction 1 lorem ipsum dolor sir amet</p>
                            <p class="contact-item__function">Fonction 2 lorem ipsum dolor consectur elis</p>
                            <p class="contact-item__function">Fonction 3 passam filis poder</p>
                    </div>
                    {% endif %}
                    <ul class="contact-item__infos">
                        {% if structure %}
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0639987845',
                                    text = '06 39 98 78 45',
                                    textSrOnly = 'Mobile',
                                    className = 'btn is-small',
                                    icon = 'far fa-mobile'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                        {% else %}
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                        {% endif %}
                    </ul>
                </div>
            {% endif %}
        </div>
    </article>
{%- endmacro -%}

{#
    Structures template.
#}
{%- macro StructuresItem(
    className = '',
    imageSizes = ['200x200?1279', '250x250'],
    category = true,
    contentInfo = true,
    hasImage = true,
    hasTime = true,
    hasWebsite = true,
    hasContactName = false,
    hasSocials = true
) -%}
    <article class="contact-item is-structure {{ className }}">
        {% if hasImage %}
            {{ Image({
                sizes: imageSizes,
                className: 'contact-item__picture',
                serviceID: range(50) | random,
                alt: ''
            }) }}
        {% endif %}
        <div class="contact-item__content">
            <div class="contact-item__content-top">
                <h3 class="contact-item__title">
                    {%- if category %}
                        <span class="contact-item__theme">Thématique</span>
                        <span class="sr-only"> : </span>
                    {%- endif %}
                    <a href="single-structures.html" class="contact-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor' }}
                        </span>
                    </a>
                </h3>
            </div>
            {% if contentInfo %}
                <div class="contact-item__content-info">
                    <div class="contact-item__details">
                            {% call InfoBlock() %}
                                {{ InfoItem(type = 'address') }}
                                {% if hasContactName %}
                                    {{ InfoItem(type = 'contact') }}
                                {% endif %}
                                {% if hasTime %}
                                    {{ InfoItem(type = 'hours') }}
                                {% endif %}
                            {% endcall %}
                            {% call InfoBlock() %}
                                {% if hasWebsite %}
                                    {{ InfoItem(type = 'website') }}
                                {% endif %}
                            {% endcall %}
                    </div>
                    <div class="contact-item__details">
                        {% if hasSocials %}
                            <div class="contact-item__socials"> {{ SocialMenu() }} </div>
                        {% endif %}
                        <ul class="contact-item__infos">
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0639987845',
                                    text = '06 39 98 78 45',
                                    textSrOnly = 'Mobile',
                                    className = 'btn is-small',
                                    icon = 'far fa-mobile'
                                ) }}
                            </li>
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                        </ul>
                    </div>
                </div>
            {% endif %}
        </div>
    </article>
{%- endmacro -%}

{#
    equipments template.
#}
{%- macro EquipmentsItem(
    className = '',
    category = true,
    contentInfo = true,
    commune = false,
    name = false,
    email = false,
    address = true,
    link = ''
) -%}
    <article class="contact-item is-structure {{ className }}">
        <div class="contact-item__content">
            <div class="contact-item__content-top">
                <h3 class="contact-item__title">
                    {%- if category %}
                        <span class="contact-item__theme">Thématique</span>
                        <span class="sr-only"> : </span>
                    {%- endif %}
                    <a href="{{ link }}" class="contact-item__title-link">
                        <span class="underline">
                            {{ 'Titre de la fiche lorem ipsum dolor' }}
                        </span>
                    </a>
                </h3>
            </div>
            {% if contentInfo %}
                <div class="contact-item__content-info">
                {%- if commune %}
                    <div class="contact-item__details">
                            {% call InfoBlock() %}
                                {{ InfoItem(type = 'commune') }}
                            {% endcall %}
                    </div>
                {%- endif %}
                {%- if address %}
                    <div class="contact-item__details">
                        {% call InfoBlock() %}
                            {{ InfoItem(type = 'address') }}
                        {% endcall %}
                    </div>
                {%- endif %}
                {%- if name %}
                    <div class="contact-item__details">
                        {{ CreateInfoItem(
                            icon= 'far fa-user',
                            text= 'Nom contact'
                        ) }}
                    </div>
                {%- endif %}
                    <div class="contact-item__details">
                        <ul class="contact-item__infos">
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'tel:0494000000',
                                    text = '04 94 00 00 00',
                                    textSrOnly = 'Téléphone',
                                    className = 'btn is-small',
                                    icon = 'far fa-phone'
                                ) }}
                            </li>
                        {%- if email %}
                            <li class="contact-item__infos-item">
                                {{ Link(
                                    href = 'mailto:<EMAIL>',
                                    text = 'Courriel',
                                    className = 'btn is-small',
                                    icon = 'far fa-at'
                                ) }}
                            </li>
                        {%- endif %}
                        </ul>
                    </div>
                </div>
            {% endif %}
        </div>
    </article>
{%- endmacro -%}

{#
    ContactList template.
#}
{%- macro ContactsList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 6
) -%}
    {% call List(
        itemClass = itemClass,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        count = count
        ) %}
        {{ ContactsItem() }}
    {% endcall %}
{%- endmacro -%}


{#
    Structures template.
#}
{%- macro StructuresList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 4
) -%}
    {% call List(
        itemClass = itemClass,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        count = count
        ) %}
        {{ ContactsItem(imageSizes = ['250x166'], structure = true) }}
    {% endcall %}
{%- endmacro -%}

{#
    equipments template.
#}
{%- macro ElementMapList(
    itemClass = 'has-mb-1',
    cols = 2,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 8,
    commune = false,
    name = false,
    email = false,
    address = true,
    link = 'single-equipment.html'
) -%}
    {% call List(
        itemClass = itemClass,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        count = count
        ) %}
        {{ EquipmentsItem(link = link, commune = commune, name = name, email = email, address = address) }}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsContent template.
#}
{%- macro ContactsContent(
    className = 'contact-content',
    titleText = 'Contact',
    itemsCount = 1,
    proposerButton = false,
    moreButton = false
) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText,
                iconPath = false
            ) }}
        </div>
        <div class="section__content">
            {{ ContactsList(
                itemClass = '',
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = 1,
                xsCols = false
            ) }}
        </div>
        {% if moreButton or proposerButton %}
            <div class="section__more-links">
                {% if proposerButton %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsStructuresContent template.
#}
{%- macro ContactsStructuresContent(
    className = 'contact-section',
    titleText = 'Structure',
    itemsCount = 1,
    proposerButton = false,
    moreButton = false,
    hasImage = true,
    hasTime = true,
    hasWebsite = true,
    hasContactName = false,
    hasSocials = true
) -%}
    {% call Section(className = className, container = false) %}
        <div class="section__title">
            {{ TitleRTE(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {% call List(
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = false,
                xsCols = false
            ) %}
                {{ StructuresItem(imageSizes = ['200x133'], hasImage = hasImage, hasTime = hasTime, hasWebsite = hasWebsite, hasContactName = hasContactName , hasSocials = hasSocials) }}
            {% endcall %}
        </div>
        {% if moreButton or proposerButton %}
            <div class="section__more-links">
                {% if proposerButton %}
                    {{ Link(
                        href = 'page-proposer.html',
                        text = 'Proposer un structure',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        href = 'list-contacts.html',
                        text = 'Toutes les structures',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    ContactsSidebar template.
#}
{%- macro ContactsSidebar(
    titleText = 'Contact',
    itemsCount = 1,
    moreButton = false,
    proposerButton = false
) -%}
    {% call Widget(className = 'contact-widget') %}
        <div class="widget__title">
            {{ TitleWidget(
                className = 'is-center',
                text = titleText,
                iconPath = false
            ) }}
        </div>
        <div class="widget__content">
            {{ ContactsList(
                itemClass = '',
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = false,
                xsCols = false
            ) }}
        </div>
        {% if moreButton or proposerButton %}
            <div class="widget__more-links">
                {% if proposerButton %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}


{#
    ContactsStructuresSidebar template.
#}
{%- macro ContactsStructuresSidebar(
    titleText = 'Structure',
    itemsCount = 1,
    moreButton = false,
    proposerButton = false
) -%}
    {% call Widget(className = 'contact-widget') %}
        <div class="widget__title">
            {{ TitleWidget(
                className = 'is-center',
                text = titleText,
                iconPath = false
            ) }}
        </div>
        <div class="widget__content">
            {% call List(
                count = itemsCount,
                cols = 1,
                mdCols = false,
                smCols = false,
                xsCols = false
                ) %}
                {{ ContactsItem({ imageSizes: ['250x200'], structure: true }) }}
            {% endcall %}
        </div>
        {% if moreButton or proposerButton %}
            <div class="widget__more-links">
                {% if proposerButton %}
                    {{ Link(
                        href = kGlobalLinks.proposer,
                        text = 'Proposer un contact',
                        className = 'btn',
                        icon = 'far fa-calendar-plus'
                    ) }}
                {% endif %}
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listContacts,
                        text = 'Toutes les contacts',
                        className = 'btn',
                        icon = 'far fa-plus-circle'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{%- endmacro -%}

{#
    CommunesList template.
#}
{%- macro CommunesList(
    itemClass = 'has-mb-1',
    cols = 1,
    mdCols = 1,
    smCols = 1,
    xsCols = 1,
    count = 4,
    hasImage = true,
    hasTime = true,
    hasWebsite = true,
    hasContactName = false,
    hasSocials = true
) -%}
    {% call List(
        itemClass = itemClass,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols,
        count = count
        ) %}
        {{ StructuresItem(imageSizes = ['200x133'], hasImage = hasImage, hasTime = hasTime, hasWebsite = hasWebsite, hasContactName = hasContactName , hasSocials = hasSocials) }}
    {% endcall %}
{%- endmacro -%}

{%- macro ContactsItemSmall(
    className = '',
    name = 'Mme Isabelle Vodor',
    function = 'Fonction Lorem'
) -%}
    <article class="contact-item-small {{ className }}">
        {{ Image({
            className: 'contact-item-small__picture avatar-image',
            image: ['organigramme-placeholder.svg'],
            alt: ''
        }) }}        
        <div class="contact-item-small__content">
            <h3 class="contact-item-small__name">{{ name }}</h3>
            <p class="contact-item-small__function">{{ function }}</p>
        </div>
    </article>
{%- endmacro -%}
