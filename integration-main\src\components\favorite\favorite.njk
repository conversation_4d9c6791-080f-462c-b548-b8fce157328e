{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'components/share/share.njk' import Share -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/section.njk' import Section -%}
{%- import 'views/core-components/title.njk' as Title -%}

{%- macro FavoriteItem(
    imageSizes = ['480x320'],
    category = lorem(range(1, 3) | random, 'words'),
    title = lorem(1),
    date = false
) -%}
    {% set titleUid = Helpers.unique() %}
    <article class="favorite-item">
        <div class="favorite-item__content">
            <h3 class="item-title favorite-item__title" id="{{ titleUid }}">
                {% if category %}
                    <span class="theme favorite-item__category">{{ category }}</span>
                    <span class="sr-only">:</span>
                {% endif %}
                <a href="#" class="underline">{{ title }}</a>
            </h3>
            {% if date %}
                <p class="favorite-item__date">
                    <span>Sauvegard<PERSON> le </span>
                    <time datetime="2022-09-22">22/09/2022</time>
                </p>
            {% endif %}
        </div>
        {{ Image({
            className: 'favorite-item__image',
            sizes: imageSizes,
            type: 'default',
            alt: 'image alt',
            serviceID: range(50) | random
        }) }}
        {{ Button(
            className = 'favorite-item__remove',
            icon = 'far fa-trash',
            text = 'Supprimer',
            attrs = {
                'data-content': 'Supprimer',
                'data-src': '#remove-item',
                'data-fancybox-remove': 'true',
                'aria-describedby': titleUid
            }
        ) }}
    </article>
{%- endmacro -%}

{#
    FavoritesList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro FavoritesList(
    itemClass = 'has-mb-4',
    count = 6,
    cols = 3,
    mdCols = 1,
    smCols = false,
    xsCols = false
) -%}
    {% call List(
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
    ) %}
        {{ FavoriteItem() }}
    {% endcall %}
{%- endmacro -%}

{#
    FavoritesSection template.
    Template for favorites on personal account.
    @param {string} titleText - section title
#}
{%- macro FavoritesSection(
    titleText = 'Mes actualités'
) -%}
    {% call Section(className = 'account-section', container = false) %}
        <div class="section__title">
            {{ Title.TitleDefault(
                className = 'as-legend',
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ FavoritesList() }}
        </div>
    {% endcall %}
{%- endmacro -%}
