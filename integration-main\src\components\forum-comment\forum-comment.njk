{%- import 'views/core-components/form.njk' as Form -%}
{%- import 'views/core-components/filter.njk' as Filter -%}
{%- from 'views/core-components/button.njk' import Button -%}

{#
    ForumAddComment template.
#}
{%- macro ForumAddComment() -%}
    <article class="forum-comment-item">
        <div class="forum-comment-item__title">
            <h3 class="item-title forum-comment-item__title">
                <span class="underline">Ajouter un commentaire</span>
            </h3>
        </div>
        <div class="forum-comment-item__content">
            {%- call Form.FormWrapper(
                    legend = false,
                    className = "js-validator-form event-message__form"
                ) -%}
            <div class="col-xs-12">
                {{ Form.FormField(
                        type = 'textarea',
                        label = 'Ajouter un commentaire ',
                        id = 'identical-field-email',
                        required = true,
                        fieldClass = 'textarea-counter'
                ) }}
            </div>
            <div class="forum-comment-item__content-files">
                    {{ Form.FormField(
                    label = 'Votre pièce jointe 1',
                    type = 'file-default',
                    textHelp = 'jpg, gif, png, bmp, rtf, doc, docx, xls, xlsx, pdf, zip (jusqu\'à 10 Mo)',
                    fieldClass = 'file-input'
                ) }}
                {{ Form.FormField(
                    label = 'Votre pièce jointe 2',
                    type = 'file-default',
                    textHelp = 'jpg, gif, png, bmp, rtf, doc, docx, xls, xlsx, pdf, zip (jusqu\'à 10 Mo)',
                    fieldClass = 'file-input'
                ) }}
            </div>
            <div class="form__field-wrapper checkbox-wrapper">
                {{ Form.RadioCheckbox(
                            label = 'Je reconnais avoir pris connaissance de la politique du site en matière de protection des données, et je consens à l’usage de mes données.
                            <a href="/fr/politique-de-gestion-des-donnees-personnelles" target="_blank">Cliquez ici pour les consulter</a>
                            <em class="required" aria-hidden="true">(obligatoire)</em>',
                            required = true
                        ) }}
            </div>
            <div class="col-md-12 col-xs-12">
                <div class="form__field-wrapper">
                    ###PASTE CAPCHA HERE
                </div>
            </div>
            {% call Form.FormActions(className = 'is-right') %}
                {{ Button(className = 'btn is-primary', type = 'submit', text = 'ENVOYER') }}
            {% endcall %}
            {%- endcall -%}
        </div>
    </article>
{%- endmacro -%}