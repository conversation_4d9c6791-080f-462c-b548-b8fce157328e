import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
export class MapList {

    init(): void {
        this.changeToggelInTheFilterList();
        this.clickToTheCloseButtonInTheFilterList();
        this.handleFilterSelect();
        this.clickToMoreFilters();
    }

    changeToggelInTheFilterList(): void {
        // get the selector list and the map elements
        const mapElements = document.querySelector('.list-content__map');
        const selectorList = document.querySelector('.list-wrapper');

        if (!selectorList || !mapElements) {
            return;
        }

        // make the map elements hidden by default
        mapElements.classList.add('is-hidden');

        const radioButton = document.getElementById('toggle-map');
        if (!radioButton || !radioButton.nextElementSibling?.nextElementSibling) {
            return;
        }
        const radioCircle = radioButton.nextElementSibling.nextElementSibling;

        // Initialize the variable to track the checked state
        let isChecked = false;

        // Update aria-checked when the radio button changes state
        radioButton.addEventListener('click', () => {
            // Toggle the checked state
            isChecked = !isChecked;

            // Set the aria-checked attribute manually based on the state
            radioButton.setAttribute('aria-checked', isChecked.toString());

            // Add/remove classes based on checked state
            if (isChecked) {
                // When checked, add the 'active' class and remove 'inactive'
                radioCircle.classList.add('active');
                radioCircle.classList.remove('inactive');

                // make the map elements visible and the selector list hidden
                mapElements.classList.remove('is-hidden');
                selectorList.classList.add('is-hidden');
            } else {
                // When unchecked, add the 'inactive' class and remove 'active'
                radioCircle.classList.add('inactive');
                radioCircle.classList.remove('active');

                // make the map elements hidden and the selector list visible
                mapElements.classList.add('is-hidden');
                selectorList.classList.remove('is-hidden');
            }
        });
    }

    clickToTheCloseButtonInTheFilterList(): void {
        const closeButton = document.querySelector('.map-template-heading__close');
        const selectorList = document.querySelector('.map-cluster-elements');

        if (!closeButton || !selectorList) {
            return;
        }

        closeButton.addEventListener('click', () => {
            selectorList.classList.add('is-hidden');
        });
    }

    /**
 * Handle navbar links with single-selection logic.
 */
    handleFilterSelect(): void {
        const listSelectors: NodeListOf<HTMLElement> = document.querySelectorAll('.search-aside__select');

        if (!listSelectors.length) {
            return;
        }

        // Loop through each .search-aside__select element
        listSelectors.forEach((listSelector: HTMLElement) => {
            const checkboxes: NodeListOf<HTMLInputElement> = listSelector.querySelectorAll('.radio-checkbox__input');

            if (!checkboxes.length) {
                return;
            }

            // Loop through all the checkbox elements and add the event listener
            checkboxes.forEach((checkbox: HTMLInputElement) => {
                checkbox.addEventListener('change', (e: Event) => {
                    const target = e.target as HTMLInputElement;
                    const label = document.querySelector(`label[for="${target.id}"]`) as HTMLElement;

                    // Check if the checkbox is checked
                    if (target.checked) {
                        addClass(label, 'is-filter-active');
                    } else {
                        removeClass(label, 'is-filter-active');
                    }
                });
            });
        });
    }

    clickToMoreFilters(): void {
        const moreFilters: HTMLElement | null = document.querySelector('.search-aside__group-thematique-title-more');
        const groupOfExtremeSearch: HTMLElement | null = document.querySelector('.search-aside__group-of-extreme-search');

        if (!moreFilters || !groupOfExtremeSearch) {
            return;
        }

        moreFilters.addEventListener('click', () => {

            if (hasClass(groupOfExtremeSearch, 'is-hidden-filters')) {
                removeClass(groupOfExtremeSearch, 'is-hidden-filters');
            } else {
                addClass(groupOfExtremeSearch, 'is-hidden-filters');
            }
        });
    }
}

