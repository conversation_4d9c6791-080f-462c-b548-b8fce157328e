$chartDataColors: (
    '1': var(--color-1--1),
    '2': var(--color-1--4),
    '3': var(--color-2--2),
    '4': var(--color-2--1),
    '5': var(--color-2--3),
    '6': var(--color-1--3),
);
    
.chart-data {
    $this: &;

    &__item {
        align-items: center;
        display: flex;
        font-size: 1.6rem;
        margin: 0 0 6px;
        padding-left: 40px;
        position: relative;

        strong {
            display: inline-block;
            font-size: 2rem;
            font-weight: var(--fw-bold);
            margin-right: 6px;
            min-width: 50px;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__colorbox {
        @include absolute(-1px, null, null, 0);
        @include size(30px);
        border-radius: 50%;
        display: block;
    }

    @each $index, $color in $chartDataColors {
        &__item:nth-child(#{$index}) #{$this}__colorbox {
            background-color: $color;
        }
    }
}

.chart-tooltip {
    background: rgba($color-black, 0.9);
    border-radius: 5px;
    color: $color-white;
    display: block;
    font-size: 1.4rem;
    max-width: 200px;
    padding: 7px 10px;

    span {
        @include size(15px);
        border: 1px solid $color-white;
        display: inline-block;
        margin-right: 5px;
        vertical-align: text-bottom;
    }
}
