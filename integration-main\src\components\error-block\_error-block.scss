.error-block {
    + .error-block {
        margin-top: 100px;
    }

    &__wrapper {
        align-items: flex-start;
        display: flex;

        @include breakpoint(medium down) {
            display: block;
            text-align: center;
        }
    }

    &__picture {
        @include size(410px);
        display: block;
        flex-shrink: 0;
        margin: 0 60px 0 0;

        @include breakpoint(medium down) {
            @include size(328px);
            margin: 0 auto;
        }

        @include breakpoint(small down) {
            @include size(164px);
        }

        svg {
            @include size(100%);
            display: block;

            .color-1--1 {
                fill: var(--color-1--1); // #4269e2
            }

            .color-1--2 {
                fill: var(--color-1--2); // #152b51
            }

            .color-1--3 {
                fill: var(--color-1--3); // #c7d2f6
                stop-color: var(--color-1--3); // #c7d2f6
            }

            .color-1--4 {
                fill: var(--color-1--4); // #2e499e
            }

            .color-2--1 {
                fill: var(--color-2--1); // #ff8977
            }

            .color-2--3 {
                fill: var(--color-2--3); // #ffdcd6
            }
        }

        &.is-custom {
            border-radius: 50%;
            overflow: hidden;

            @include breakpoint(large only) {
                @include size(434px);
            }
        }
    }

    &__title {
        @include font(var(--typo-1), 7.5rem, var(--fw-black));
        color: $color-3--4;
        line-height: 1;
        margin: 0 0 20px;

        @include breakpoint(medium down) {
            font-size: 5.5rem;
            line-height: 5.8rem;
            margin: 0 0 10px;
        }

        @include breakpoint(small down) {
            font-size: 3.8rem;
            line-height: 4rem;
        }
    }

    &__subtitle {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        color: $color-black;
        line-height: 5rem;
        margin: 0;
        max-width: 90%;

        @include breakpoint(medium down) {
            font-size: 3.5rem;
            line-height: 4rem;
            max-width: none;
        }

        @include breakpoint(small down) {
            font-size: 2.8rem;
            line-height: 3.2rem;
        }

        span {
            color: var(--color-1--1);
        }
    }

    &__teaser {
        @include font(var(--typo-1), 2.4rem, var(--fw-bold));
        color: $color-3--4;
        line-height: 3.6rem;
        margin: 30px 0 40px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: 3.4rem;
        }

        @include breakpoint(small down) {
            font-size: 1.8rem;
            line-height: 2.6rem;
        }
    }

    &__buttons {
        @include breakpoint(medium down) {
            justify-content: center;
        }
    }

    &__text-bottom {
        @include font(var(--typo-1), 1.8rem, var(--fw-normal));
        color: $color-black;
        line-height: 3rem;
        margin: 90px auto 0;
        max-width: 85%;
        text-align: center;

        @include breakpoint(medium down) {
            margin-top: 80px;
        }

        @include breakpoint(small down) {
            margin-top: 60px;
            max-width: none;
        }

        a {
            @include on-event {
                text-decoration: none;
            }
        }
    }
}
