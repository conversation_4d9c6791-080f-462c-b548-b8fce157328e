//genial decor for home style
@mixin home-styled-decor {
    &::before {
        @include absolute(0, null, null, 50%);
        @include size(1000vw, calc(100% - 111px));
        background-color: $color-white;
        content: "";
        transform: translateX(-500vw);
        z-index: -1;

        @include breakpoint(medium down) {
            background-color: var(--color-1--2);
            height: 100%;
        }

        @include breakpoint(small down) {
            height: calc(100% + 20px);
            top: -20px;
        }
    }
}

.event-focus {
    $this: &;
    padding-bottom: 15px;
    position: relative;

    @include breakpoint(medium down) {
        padding-bottom: 30px;
    }

    @include breakpoint(small down) {
        padding-bottom: 7px;
        
        &::before {
            @include absolute(31px, null, null, 50%);
            @include size(100vw, calc(100% - 31px));
            background-color: var(--color-1--1);
            content: "";
            transform: translateX(-50%);
        }

        &::after {
            @include absolute(108px, -156px);
            @include size(210px, 330px);
            background-image: image("motif-agenda.svg");
            background-repeat: no-repeat;
            background-size: contain;
            content: "";
            transform: rotate(-22deg);
            z-index: 1;
        }
    }

    // styles for HP with background
    .events-home & {
        padding-bottom: 26px;

        @include breakpoint(medium down) {
            padding-bottom: 3px;
        }

        @include breakpoint(small down) {
            padding-bottom: 17px;
        }
        
        &::before {
            @include absolute(null, null, 0, 50%);
            @include size(100vw, calc(100% - 184px));
            background-color: var(--color-1--1);
            content: "";
            transform: translateX(-50%);

            @include breakpoint(medium down) {
                height: 72%;
            }

            @include breakpoint(small down) {
                height: calc(100% - 35px);
            }
        }

        #{$this} {
            &__text {
                max-width: 210px;

                @include breakpoint(medium down) {
                    background-color: var(--color-1--1);
                    max-width: 289px;
                }
            }

            &__content {
                @include breakpoint(large only) {
                    @include absolute(null, -60px, 0, null);
                    margin: 0;
                    min-height: 298px;
                    padding: 62px 5px 20px 25px;
                }
            }

            &__category {
                margin-bottom: 5px;
            }

            &__title {
                @include breakpoint(medium down) {
                    color: $color-white;
                }

                .underline {
                    @include breakpoint(medium down) {
                        @include multiline-underline();
                    }
                }
            }

            &__time-place {
                .time-place__item {
                    @include breakpoint(medium down) {
                        color: var(--color-1--3);
                    }
                }
            }

            &__date {
                margin-top: 10px;

                @include breakpoint(medium down) {
                    margin-top: 2px;
                }
            }
        }
    }

    &__wrapper {
        @extend %link-block-context;
        align-items: center;
        display: flex;
        flex-direction: column-reverse;
        position: relative;
        z-index: 1;

        @include breakpoint(small down) {
            padding: 0;
        }

        @include on-event() {
            #{$this}__title-link {
                .underline {
                    background-size: 100% 100%;
                }
            }
        }
    }

    &__picture-link {
        flex-shrink: 0;
        overflow: hidden;
        width: 1200px;
        z-index: -1;

        @include breakpoint(medium down) {
            width: 100%;
        }
    }

    &__picture {
        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__content {
        background-color: var(--color-1--1);
        display: flex;
        flex-direction: row-reverse;
        flex-grow: 1;
        margin: -119px auto 0;
        max-width: 646px;
        padding: 52px 54px 47px 45px;
        position: relative;
        word-break: break-word;

        @include breakpoint(medium down) {
            justify-content: flex-end;
            margin: -40px 0 0;
            padding: 25px 62px 13px 26px;
        }

        @include breakpoint(medium only) {
            width: 587px;
        }

        @include breakpoint(small down) {
            margin: 0;
            padding: 16px 55px 49px 0;
        }
    }

    &__date {
        border-right: 1px solid var(--color-2--1);

        @include breakpoint(small down) {
            border: none;
            position: relative;

            &::after {
                @include absolute(50%, 0);
                @include size(1px, 99px);
                background-color: var(--color-2--1);
                content: "";
                transform: translateY(-50%);
            }
        }
    }

    &__text {
        background-color: var(--color-1--1);
        margin-left: 32px;

        @include breakpoint(medium down) {
            margin-left: 30px;
        }

        @include breakpoint(small down) {
            margin-left: 17px;
            width: auto;
        }
    }

    &__title {
        font-size: 3rem;
        line-height: 1.2;

        @include breakpoint(medium down) {
            font-size: 2.6rem;
            line-height: 1.23;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
        color: $color-white;
    }

    &__category {
        @include font(null, 1.4rem, var(--fw-medium));
        color: $color-white;
        line-height: 2rem;
    }

    &__time-place {
        margin-top: 5px;

        .time-place__item {
            color: $color-white;
            margin-right: 16px;

            &.is-time {
                flex-shrink: 0;
            }
        }
    }

    &__more-links {
        @include breakpoint(large only) {
            justify-content: flex-end;
        }

        @include breakpoint(small down) {
            margin-top: 35px;
        }
    }

    &__actions {
        @include absolute(35px, 45px);
        z-index: 11;
    }
}
