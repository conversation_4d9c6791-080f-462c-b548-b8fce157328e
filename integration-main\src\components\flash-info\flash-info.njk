{%- from 'views/utils/utils.njk' import svg -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'views/core-components/button.njk' import Button -%}
{%- from 'views/utils/utils.njk' import setAttr -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- import 'views/utils/styleguide-helpers.njk' as SG -%}

{#
    FlashInfoItem template default
#}
{%- macro FlashInfoItem(
    popup = false,
    hasImage = true,
    imageSizes = ['110x73'],
    uid = Helpers.unique()
) -%}
    <article id="flash-item-{{ uid }}" class="flash-info-item">
        <div class="flash-info-item__wrapper">
            {%- if hasImage -%}
                {{ Image({
                    className: 'flash-info-item__image',
                    sizes: imageSizes,
                    alt: '',
                    hasLazyLoad: false
                }) }}
            {%- endif -%}
            <div class="flash-info-item__content">
                <h3 class="flash-info-item__title" id="title-{{ uid }}">Titre alerte 55 caractères max dolor se elmentum di lec...</h3>
                <p class="flash-info-item__teaser">Texte alerte 150 caracères max, dolor sed elementum congue, lectuses tellus posuere dui, et bibendum tortor velit non neque. Fusce caliquam rutrum sit...</p>
                {%- if popup -%}
                    {{ Link(
                        href = '#',
                        text = 'En savoir plus',
                        className = 'btn is-small is-inverted flash-info-item__link',
                        icon = false,
                        attrs = {
                            'aria-describedby': 'title-' + uid
                        }
                    ) }}
                {%- endif -%}
            </div>
        </div>
        {%- if not popup -%}
            {{ Link(
                href = '#',
                text = 'En savoir plus',
                className = 'btn is-small is-inverted flash-info-item__link',
                icon = false,
                attrs = {
                    'aria-describedby': 'title-' + uid
                }
            ) }}
        {%- endif -%}
        {% call Button(
            text = 'En savoir plus',
            className = 'flash-info-item__close js-flash-info-close',
            icon = 'far fa-times',
            attrs = {
                'aria-describedby': 'title-' + uid
            }
        ) %}
            <span class="ghost">Ne plus afficher</span>
        {% endcall %}
    </article>
{% endmacro %}

{#
    FlashInfo template
#}
{%- macro FlashInfo(
    containerId = 'flash-info-container',
    modifier = '',
    popup = false,
    hasImage = true,
    imageSizes = ['110x73']
) -%}
    {% set uid = Helpers.unique() %}
    <section class="flash-info {{ modifier }}"
             id="{{ containerId }}"
             {{ setAttr('aria-modal', 'true') if popup }}
             {{ setAttr('role', 'dialog') if popup }}
             {{ setAttr('aria-labelledby', uid) if popup }}
    >
        <div class="flash-info__wrapper">
            <div class="flash-info__title">
                <div class="flash-info__title-svg" aria-hidden="true">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <path d="M256,0C114.84,0,0,114.84,0,256s114.84,256,256,256,256-114.84,256-256S397.16,0,256,0Zm0,495.48c-132.05,0-239.48-107.43-239.48-239.48S123.95,16.52,256,16.52s239.48,107.43,239.48,239.48-107.44,239.48-239.48,239.48Zm42.2-305.07h-110v49.27l35.06,11.69v93.29l-35.06,11.66v49.28l145.07,.02v-49.27l-35.06-11.69V190.41Zm18.55,177.84v20.85l-112.04-.02v-20.85l35.06-11.66v-117.11l-35.06-11.69v-20.85h76.97v149.64l35.06,11.69Zm-64.88-194.83c25.04,0,45.42-20.38,45.42-45.42s-20.38-45.42-45.42-45.42-45.42,20.38-45.42,45.42,20.38,45.42,45.42,45.42Zm0-74.32c15.94,0,28.9,12.97,28.9,28.9s-12.97,28.9-28.9,28.9-28.9-12.97-28.9-28.9,12.97-28.9,28.9-28.9Z"/>
                    </svg>
                </div>
                <p class="flash-info__title-text" role="heading" aria-level="2" {{ setAttr('id', uid) if popup }}>Flash infos</p>
            </div>
            {{ FlashInfoItem(hasImage = hasImage, imageSizes = imageSizes, popup = popup) }}
        </div>
    </section>
{%- endmacro -%}

{#
    FlashInfoSG template.
    FlashInfo template for styleguide.
#}
{%- macro FlashInfoSG() -%}
    {% call SG.Section('flash-info') %}
        <h2 class="styleguide-section__title">Flash info</h2>
        {% call SG.Preview() %}
            <div class="flex-row">
                <div class="col-xs-12">
                    {{ FlashInfo() }}
                </div>
            </div>
        {% endcall %}
    {% endcall %}
{%- endmacro -%}
