.forum-comment-item {
    background-color: $color-3--1;
    margin-top: 86px;
    padding: 55px 50px 44px 47px;
    position: relative;

    @include breakpoint(medium down) {
        padding: 55px 30px 44px 30px;
    }

    &__title {
        @include font(var(--typo-1), 2.6rem, var(--fw-bold));
        color: var(--color-1--1);
        margin: 0 0 44px;
    }

    .form__field-wrapper {
        .textarea-counter {
            @include size(1100px,157px);
            margin-bottom: 4px;

            @include breakpoint(medium down) {
                @include size(100%, auto);
            }
        }
    }

    .col-xs-12,
    .col-xs-6 {
        padding-left: 0;
    }

    &__content {
        &-files {
            display: flex;
            gap: 40px;
            margin-bottom: 12px;

            @include breakpoint(medium down) {
                flex-direction: column;
            }

            .file-input {
                @include size(384px, 63px);

                @include breakpoint(medium down) {
                    @include size(100%, auto);
                }
            }
        }
    }

    .checkbox-wrapper {
        max-width: 826px;
    }

    .btn {
        @include size(183px,60px);
        left: -3px;
        position: relative;
        top: -3px;

        @include breakpoint(medium down) {
            position: static;
        }
    }
}

.button-forum {
    margin-bottom: 69px;
    margin-top: 45px;
    text-align: center;
    width: 100%;
}
