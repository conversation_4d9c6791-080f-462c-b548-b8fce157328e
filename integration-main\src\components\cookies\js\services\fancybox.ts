import { cookiesToObject } from '../utils/transform.utils';
import { setCookiePlaceholder } from '../utils/cookie.utils';

declare let tarteaucitron;

/**
 * Custom fancybox cookie service for tarteaucitron plugin.
 */
export default class FancyboxCookie {
    public constructor() {
        try {
            this.init();
        } catch (error) {
            console.error(error);
        }
    }

    /**
     * Set service cookies according dependencies
     */
    public update(): void {
        if (typeof tarteaucitron === 'undefined') {
            throw new Error('tarteaucitron object does not exists');
        }

        const cookies = cookiesToObject(tarteaucitron.cookie.read());
        const dependencies = [
            'youtube',
            'youtu',
            'vimeo',
            'dailymotion',
            'stratis-youtube',
            'stratis-vimeo',
            'stratis-dailymotion',
        ];
        const hasDisabledServices = dependencies.some(service => cookies[service] === 'false' || cookies[service] === 'wait');

        if (hasDisabledServices || !Object.keys(cookies).length) {
            tarteaucitron.cookie.create('fancybox', false);
        }
    }

    /**
     * Handle service items if cookies accepted.
     */
    private acceptedHandler(): void {
        // acceptedHandler
    }

    /**
     * Handle service items if cookies denied.
     */
    private deniedHandler(): void {
        // eslint-disable-next-line sonarjs/cognitive-complexity
        tarteaucitron.fallback(['js-fancybox-check-cookies'], item => {
            const matches = item.href.match(/youtube|youtu|vimeo|dailymotion/ig);

            if (matches) {
                const [service] = matches;
                const cookies = cookiesToObject(tarteaucitron.cookie.read());

                if (cookies[service] === 'true' || cookies[`stratis-${service === 'youtu' ? 'youtube' : service}`] === 'true') {
                    return item;
                }

                const itemToPaste = setCookiePlaceholder(`stratis-${service === 'youtu' ? 'youtube' : service}`, item);

                item.parentElement.replaceChild(itemToPaste, item);

                const galleryList = itemToPaste.classList.contains('gallery-item')
                    ? itemToPaste.parentElement!.parentElement?.parentElement
                    : itemToPaste.parentElement?.parentElement;

                if (galleryList) {
                    const haveLinks = galleryList.querySelector('a');
                    const hasBlockedElements = galleryList.querySelector('button');
                    const hasCookieManagerButton = galleryList.parentElement!.querySelector('.js-open-cookie-manager');

                    if (!haveLinks) {
                        galleryList.classList.add('no-links');
                    }

                    if (hasBlockedElements) {
                        galleryList.classList.add('has-blocked-elements');
                    }

                    if (!hasCookieManagerButton) {
                        const cookieManagerButtonTpl = `
                            <div class="gallery__actions ttc-open-cookie-manager-wrapper">
                                <button type="button" class="btn is-primary js-open-cookie-manager" onclick="tarteaucitron.userInterface.openPanel()">
                                    ${tarteaucitron.lang['open-cookie-manager-button']}
                                </button>
                            </div>
                        `;

                        galleryList.insertAdjacentHTML('afterend', cookieManagerButtonTpl.trim());
                    }
                }

                return itemToPaste;
            }
        }, true);
    }

    /**
     * Add event listeners for service.
     */
    private addListeners(): void {
        tarteaucitron.loadListeners.push(this.update);

        ['tac.close_panel', 'tac.root_available'].forEach(ev => {
            window.addEventListener(ev, this.update.bind(this));
        });
    }

    /**
     * Init service.
     */
    private init(): void {
        // const $this = this;

        tarteaucitron.services.fancybox = {
            key: 'fancybox',
            type: 'video',
            name: 'FancyboxService',
            needConsent: false,
            cookies: [],
            js: () => this.acceptedHandler(),
            fallback: () => this.deniedHandler(),
        };

        this.addListeners();
    }
}
