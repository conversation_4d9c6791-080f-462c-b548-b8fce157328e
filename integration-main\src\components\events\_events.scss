.events-home {
    overflow: hidden;
    padding: 0 0 50px;
    position: relative;

    &::before {
        @include absolute(129px, null, null, -10px);
        @include size(583px, 641px);
        background-image: image("motif-agenda.svg");
        background-repeat: no-repeat;
        background-size: cover;
        content: "";
        z-index: 1;

        @include breakpoint(medium down) {
            @include size(327px, 420px);
            left: -89px;
            top: 419px;
        }

        @include breakpoint(small down) {
            content: none;
        }
    }

    &__container {
        @extend %container;

        @include breakpoint(medium down) {
            max-width: 1130px;
        }

        @include breakpoint(small down) {
            padding: 0 20px;
        }
    }

    & &__list {
        padding-top: 20px;
        position: relative;

        @include breakpoint(small down) {
            padding-top: 34px;
        }

        &:first-child {
            padding-top: 50px;
        }

        &::before {
            @include absolute(0, null, null, 50%);
            @include size(100vw, 181px);
            background-color: var(--color-1--1);
            content: "";
            transform: translateX(-50%);
            z-index: 0;

            @include breakpoint(medium down) {
                height: 191px;
            }

            @include breakpoint(small down) {
                content: none;
            }
        }

        @include breakpoint(medium down) {
            margin: 0;
        }
    }

    & &__list-item {
        margin-bottom: 20px;

        @include breakpoint(medium down) {
            margin-bottom: 10px;
            padding: 0;
        }

        @include breakpoint(small down) {
            margin-bottom: 20px;
        }
    }

    & &__more-links {
        margin-top: 30px;

        @include breakpoint(small down) {
            margin-top: 16px;
        }
    }

    & &__eventlinks,
    & &__banner {
        margin: 70px 0;

        @include breakpoint(medium down) {
            margin: 60px 0 0;
        }
    }

    &.section {
        margin: 133px 0 53px;

        @include breakpoint(medium down) {
            margin-top: 62px;
        }

        @include breakpoint(small down) {
            margin-bottom: 23px;
        }
    }

    .section {
        &__title {
            align-items: flex-start;
            margin-bottom: -5px;
            position: relative;
            z-index: 2;

            @include breakpoint(small down) {
                margin-bottom: 0;
            }

            @include breakpoint(small down) {
                align-items: center;
                margin-bottom: 30px;
            }

            .title:not(.rte) {
                &.is-primary {
                    @include breakpoint(small down) {
                        margin-bottom: 19px;
                        margin-right: 0;
                    }
                }
            }
        }
    }

    .tag-links {
        max-width: 100%;
        padding: 5px 0;

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: column;
            gap: 0;
        }

        &__link {
            @include breakpoint(small down) {
                padding: 4.5px 5px;
            }
        }

        &__item {
            &:not(:last-child) {
                position: relative;

                @include breakpoint(small down) {
                    padding-bottom: 6px;
                }

                &::before {
                    @include absolute(50%, 0);
                    @include size(1px, 20px);
                    background: rgba($color-2--1, 0.3);
                    content: "";
                    transform: translateY(-50%);

                    @include breakpoint(small down) {
                        @include size(24px, 1px);
                        bottom: 0;
                        right: 50%;
                        top: inherit;
                        transform: translateX(50%);
                    }
                }
            }
        }
    }
}

.events-content {
    padding: 48px 0 44px;
    position: relative;

    @include breakpoint(large only) {
        padding: 48px 0 44px;
    }

    &::before {
        @include absolute(0, 50%);
        @include size(100vw, 100%);
        background-color: $color-3--1;
        content: "";
        transform: translateX(50%);
        z-index: -1;
    }

    .section {
        &__title {
            @include breakpoint(large only) {
                padding-left: 152px;
            }
        }

        &__more-links {
            @include breakpoint(large only) {
                justify-content: flex-end;
            }
        }
    }
}

.events-home,
.events-content {
    .events-list {
        @include breakpoint(large only) {
            padding: 0 152px;
        }

        &.list[class] {
            @include breakpoint(medium only) {
                margin: 0 -3px;
            }
        }

        .list__item {
            @include breakpoint(medium only) {
                padding-left: 18px;
                padding-right: 18px;

                &:first-child {
                    padding-left: 0;
                }

                &:last-child {
                    padding-right: 0;
                }
            }

            &:last-child {
                @include breakpoint(small down) {
                    margin-bottom: 0;
                }
            }
        }
    }
}

.events-widget {
    &_is-width-33 {
        @include breakpoint(large only) {
            padding-left: 51px;
        }
    }

    &.is-width-33 {
        @include breakpoint(large only) {
            .section {
                &__title {
                    margin-left: 50px;
                }
            }
        }
    }
}

.events-list {
    &.is-list {
        @include breakpoint(medium) {
            .event-item {
                flex-direction: row-reverse;
                justify-content: flex-end;
                margin: 0;
                max-width: 455px;

                &__image {
                    @include min-size(164px, 205px);
                    @include size(164px, 205px);
                }

                &__content-wrap {
                    position: relative;
                }

                &__content {
                    padding: 94px 0 10px 38px;
                    text-align: left;
                }

                &__date {
                    @include absolute(11px, null, null, calc(100% - 16px));
                    margin: 0;
                    max-height: 61px;
                }

                .date {
                    font-size: 3.2rem;
                    max-width: 210px;
                    padding: 6px 22px 11px 21px;

                    &__time {
                        flex-direction: row;
                    }

                    &__item {
                        &.is-month {
                            padding-left: 6px;
                        }
                    }
                }

                .time-place {
                    display: flex;
                    flex-wrap: wrap;

                    &__item {
                        justify-content: flex-start;
                        margin-right: 9px;
                        width: fit-content;
                    }
                }
            }
        }
    }
}

.events-focus-content {
    @include breakpoint(large only) {
        .section {
            &__more-links {
                justify-content: flex-end;
            }
        }
    }
}
