import { objectToQueryString } from '../utils/transform.utils';
import { setCookiePlaceholder } from '../utils/cookie.utils';

declare let tarteaucitron;

/**
 * Custom youtube cookie service for tarteaucitron plugin.
 */
export default class YoutubeCookie {
    public constructor() {
        try {
            this.init();
        } catch (error) {
            console.error(error);
        }
    }

    /**
     * Set service cookies according dependencies
     */
    public update(): void {
        // Add update logic here.
    }

    /**
     * Add event listeners for service.
     */
    private addListeners(): void {
        // Add listeners here.
    }

    /**
     * Handle service items if cookies accepted.
     */
    private acceptedHandler(): void {
        const elements = [...document.querySelectorAll('.js-youtube-player-stratis')];

        elements.forEach(elem => {
            const elemAttrs = {
                videoID: tarteaucitron.getElemAttr(elem, 'videoID'),
                width: tarteaucitron.getElemAttr(elem, 'width'),
                height: tarteaucitron.getElemAttr(elem, 'height'),
            };

            if (!elemAttrs.videoID) {
                return '';
            }

            const videoParams = objectToQueryString({
                theme: tarteaucitron.getElemAttr(elem, 'theme'),
                rel: tarteaucitron.getElemAttr(elem, 'rel'),
                controls: tarteaucitron.getElemAttr(elem, 'controls'),
                showinfo: tarteaucitron.getElemAttr(elem, 'showinfo'),
                autoplay: tarteaucitron.getElemAttr(elem, 'autoplay'),
                mute: tarteaucitron.getElemAttr(elem, 'mute'),
            });

            // eslint-disable-next-line max-len
            elem.innerHTML = `<iframe type="text/html" loading="lazy" src="//www.youtube-nocookie.com/embed/${elemAttrs.videoID + videoParams}" frameborder="0" allowfullscreen></iframe>`;
        });
    }

    /**
     * Handle service items if cookies denied.
     */
    private deniedHandler(): void {
        const id = 'stratis-youtube';

        const elements = [...document.querySelectorAll('.js-youtube-player-stratis')];

        elements.forEach(elem => {
            const placeholder = setCookiePlaceholder(id, elem, true);
            elem.appendChild(placeholder);
        });
    }

    /**
     * Init service.
     */
    private init(): void {
        tarteaucitron.services['stratis-youtube'] = {
            key: 'stratis-youtube',
            type: 'video',
            name: 'YouTube',
            readmoreLink: 'https://tarteaucitron.io/fr/service/youtube/',
            uri: 'https://policies.google.com/privacy',
            needConsent: true,
            cookies: ['VISITOR_INFO1_LIVE', 'YSC', 'PREF', 'GEUP'],
            js: () => this.acceptedHandler(),
            fallback: () => this.deniedHandler(),
        };

        this.addListeners();
    }
}
