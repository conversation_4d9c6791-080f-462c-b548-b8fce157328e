// Flash info layout styles
.flash-info {
    background-color: var(--color-1--1);
    color: $color-white;
    display: none;
    padding: 20px 0;
    position: relative;

    @include breakpoint(medium down) {
        padding: 20px 0 45px;
    }

    @include breakpoint(small down) {
        padding: 30px 25px 45px;
    }

    &.is-hidden {
        display: none;
    }

    &.is-visible {
        display: block;
    }

    &__wrapper {
        @extend %container;
        align-items: center;
        display: flex;

        @include breakpoint(medium down) {
            display: block;
            padding: 0 100px;
        }

        @include breakpoint(small down) {
            padding: 0;
        }
    }

    &__title {
        align-items: center;
        align-self: flex-start;
        display: flex;
        margin: 0 40px 0 0;

        @include breakpoint(medium down) {
            justify-content: center;
            margin: 0 0 25px;
        }

        @include breakpoint(small down) {
            align-items: center;
            border: 0;
            display: flex;
        }
    }

    &__title-text {
        @include font(var(--typo-1), 3.5rem, var(--fw-normal));
        line-height: 3.8rem;
        margin-left: 15px;
        max-width: 100px;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
            line-height: 3.2rem;
            margin-left: 12px;
            max-width: none;
        }
    }

    &__title-svg {
        @include size(60px);
        display: block;
        flex-shrink: 0;

        @include breakpoint(medium down) {
            @include size(42px);
        }

        svg {
            @include size(100%);
            display: block;
            fill: $color-white;
        }
    }

    &__items {
        flex-grow: 1;
        margin-top: 17px;
        min-width: 650px;
        width: 1%;

        @include breakpoint(small down) {
            min-width: 280px;
            width: 100%;
        }
    }
}
