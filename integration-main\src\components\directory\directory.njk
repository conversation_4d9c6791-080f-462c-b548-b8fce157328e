{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- from 'views/core-components/list.njk' import List -%}
{%- from 'views/core-components/image.njk' import Image -%}
{%- import 'views/core-components/infos.njk' as Info -%}

{#
    DirectoryItem template.
#}
{%- macro DirectoryItem(
    imageSizes = ['270x180']
) -%}
    <article class="directory-item">
        {{ Image({
            className: 'directory-item__picture avatar-image',
            sizes: imageSizes,
            serviceID: range(100) | random
        }) }}
        <div class="directory-item__content">
            <p class="theme directory-item__category">Surtitre</p>
            <h3 class="item-title directory-item__title">
                <a href="{{ kGlobalLinks.singleDirectory }}" class="directory-item__link underline-context">
                    <span class="underline">{{ lorem(5, 'words') | capitalize }}</span>
                </a>
            </h3>
            {% call Info.InfoBlock() %}
                {{ Info.CreateInfoItem(
                    className = 'is-primary',
                    icon = 'far fa-map-marker-alt',
                    text = 'Adresse lorem ipsum sit amert<br>00000  Ville',
                    ghost = 'Adresse :'
                ) }}
                {{ Info.InfoItem('hours', className = 'is-primary') }}
            {% endcall %}
            {{ Info.InfoBlock(
                columns = [
                    ['phone', 'fax', 'cellphone', 'route'],
                    ['email', 'website' ]
                ],
                row = false
            ) }}
        </div>
    </article>
{%- endmacro -%}

{#
    DirectoryList template.
    @param {number} count - items count.
    @param {string} cols - desktop columns count.
    @param {string} mdCols - tablet columns count.
    @param {string} smCols - mobile columns count.
    @param {string} xsCols - extrasmall devices columns count.
    @param {string} listClass - list class modifier.
    @param {string} itemClass - item class modifier.
#}
{%- macro DirectoryList(
    itemClass = 'has-mb-6',
    count = 6,
    cols = 3,
    mdCols = 2,
    smCols = 1,
    xsCols = 1
) -%}
    {% call List(
        itemClass = itemClass,
        count = count,
        cols = cols,
        mdCols = mdCols,
        smCols = smCols,
        xsCols = xsCols
        ) %}
        {{ DirectoryItem() }}
    {% endcall %}
{%- endmacro -%}
