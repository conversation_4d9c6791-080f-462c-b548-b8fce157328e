declare let tarteaucitron;

/**
 * Set custom placeholder for tarteaucitron cookies.
 * @param service {string} - cookie service key.
 * @param item {HTMLElement} - item which will be replaced with placeholder.
 * @param saveWrapper
 * @return {HTMLElement}
 */
export const setCookiePlaceholder = (service, item, saveWrapper = false): HTMLElement => {
    const elementWrapper = document.createElement('div');

    if (typeof tarteaucitron === 'undefined') {
        return elementWrapper;
    }

    let elementDisclaimer = document.querySelector('.cookies-denied__disclaimer') as HTMLElement;

    if (!elementDisclaimer) {
        document.body.insertAdjacentHTML('beforeend', '<p aria-live="polite" class="cookies-denied__disclaimer sr-only"></p>');
        elementDisclaimer = document.querySelector('.cookies-denied__disclaimer') as HTMLElement;
    }

    const [, currentService] = (() => {
        const selectedService = tarteaucitron.cookie.read()
            .split('!')
            .find(str => str.includes(service));

        return selectedService ? selectedService.split('=') : [];
    })();

    const serviceName = service.replace('stratis-', '');
    const replaceServiceName = (text): string => text.replace('{serviceName}', serviceName);

    const tempNode = document.createElement('div');
    tempNode.innerHTML = tarteaucitron.engage(service);

    const tacFloat = tempNode.querySelector('.tac_float') as HTMLElement;
    const [, { id }] = tacFloat.childNodes as any;

    const placeholder = `
        <div class="${item.className} cookies-denied ${service}">
            <div class="cookies-denied__content">
                <p class="cookies-denied__message">${replaceServiceName(tarteaucitron.lang.serviceText)}</p>
                <button
                    type="button"
                    id="${id}"
                    aria-label="${replaceServiceName(tarteaucitron.lang.btnAllowTitle)}"
                    title="${replaceServiceName(tarteaucitron.lang.btnAllowTitle)}"
                    class="tarteaucitronAllow"
                >
                    ${tarteaucitron.lang.btnAllow}
                </button>
                <button
                    type="button"
                    id="${id}"
                    aria-label="${replaceServiceName(tarteaucitron.lang.btnDenyTitle)}"
                    title="${replaceServiceName(tarteaucitron.lang.btnDenyTitle)}"
                    class="tarteaucitronDeny"
                >
                    ${tarteaucitron.lang.btnDeny}
                </button>
            </div>
        </div>
    `;

    elementWrapper.insertAdjacentHTML('beforeend', placeholder);

    const buttonDeny = elementWrapper.querySelector('.tarteaucitronDeny') as HTMLElement;
    const elementMessage = elementWrapper.querySelector('.cookies-denied__message') as HTMLElement;

    const hideBtnDeny = (message, btn): void => {
        message.innerHTML = tarteaucitron.lang.refuseCookies;
        btn.classList.add('is-hidden');
    };

    tarteaucitron.addClickEventToElement(buttonDeny, () => {
        tarteaucitron.userInterface.respond(buttonDeny, false);

        const services = [...document.querySelectorAll(`.${service}`)];
        services.forEach(svc => hideBtnDeny(svc.querySelector('.cookies-denied__message'), svc.querySelector('.tarteaucitronDeny')));
        elementMessage.setAttribute('tabindex', '-1');
        elementMessage.focus();
        elementDisclaimer.innerHTML = tarteaucitron.lang.refuseCookies;
    });

    elementMessage.innerHTML = replaceServiceName(tarteaucitron.lang.serviceText);

    if (currentService === 'false') {
        hideBtnDeny(elementMessage, buttonDeny);
    }

    if (saveWrapper) {
        tacFloat.innerHTML = '';
        tacFloat.appendChild(elementWrapper);
        return tempNode.firstElementChild as HTMLElement;
    }

    return elementWrapper.firstElementChild as HTMLElement;
};

export default {};
