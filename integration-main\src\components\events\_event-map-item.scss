.map-event-item {
    $this: &;

    @extend %link-block-context;
    cursor: pointer;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto;
    position: relative;

    @include breakpoint(medium down) {
        align-items: center;
        max-width: 100%;
    }

    @include breakpoint(small down) {
        align-items: flex-start;
        flex-direction: row-reverse;
    }

    &__wrap {
        width: 100%;
    }

    &__date {
        justify-content: flex-start;
        width: 100%;
    }

    .date__wrap {
        font-size: 3.2rem;

        .date__time {
            flex-direction: row;
            gap: 8px;
        }
    }

    &__content {
        text-align: left;
    }

    &__content-wrap {
        display: flex;
        flex-direction: column;
        padding: 12px 10px 13px 20px;

        @include breakpoint(small down) {
            padding-bottom: 40px;
        }
    }

    &__title {
        font-size: 1.6rem;
        font-weight: var(--fw-bold);
        line-height: 1.3;
        margin-bottom: 3px;
        width: 184px;

        @include breakpoint(medium down) {
            font-size: 1.6rem;
            line-height: 1.1;
            margin-bottom: 8px;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        @include font(null, 1.2rem, var(--fw-medium));
        color: var(--color-1--1);
        margin-bottom: 5px;
    }

    &__time-place {
        @include breakpoint(small down) {
            margin-top: 5px;
        }

        .time-place {
            &__item {
                color: $color-black;
                justify-content: left;
            }
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }
}
