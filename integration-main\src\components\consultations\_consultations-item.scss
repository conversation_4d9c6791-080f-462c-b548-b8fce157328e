.consultations-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    background-color: $color-3--1;
    display: flex;
    justify-content: space-between;
    padding: 35px;

    @include breakpoint(medium down) {
        flex-direction: column;
    }

    @include breakpoint(small down) {
        padding: 30px;
    }

    &__wrapper {
        @extend %link-block-context;
        padding-right: 24px;

        @include breakpoint(medium down) {
            padding: 0 0 20px;
        }
    }

    &__title {
        @include font(var(--typo-1), 2.4rem, var(--fw-bold));
        color: $color-black;
        line-height: 2.8rem;
        margin: 0 0 10px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: 2.4rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
            margin: 0 0 20px;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__teaser {
        @include font(var(--typo-1), 1.7rem, var(--fw-normal));
        color: $color-black;
        line-height: 2.6rem;

        @include breakpoint(medium down) {
            font-size: 1.6rem;
            line-height: 2.2rem;
        }

        @include breakpoint(small down) {
            font-size: 1.4rem;
            line-height: 1.8rem;
        }
    }

    &__info {
        @include size(245px, auto);
        border-left: 1px solid $color-3--3;
        flex-shrink: 0;
        padding-left: 55px;

        @include breakpoint(medium down) {
            align-items: center;
            border-left: 0;
            border-top: 1px solid $color-3--3;
            display: flex;
            padding: 20px 0 0;
            width: 100%;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            margin: 0 auto;
            max-width: 245px;
            padding: 30px 0 0;
        }
    }

    &__info-title {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        color: var(--color-1--1);
        line-height: 2.2rem;
        margin: 0 0 17px;
        max-width: 150px;

        @include breakpoint(medium down) {
            margin: 0 45px 0 0;
            max-width: none;
        }

        @include breakpoint(small down) {
            margin: 0 0 17px;
            text-align: center;
        }
    }

    a.btn {
        flex-shrink: 0;
        margin: 0 0 10px;

        @include breakpoint(medium only) {
            margin: 0 10px 0 0;
        }

        &:last-child {
            margin: 0;
        }
    }

    &.is-click-and-roll {
        background-color: transparent;
        padding: 0;

        #{$this}__title {
            color: var(--color-1--2);
            font-size: 2.6rem;
            line-height: 3.4rem;

            @include breakpoint(small down) {
                font-size: 2.3rem;
                line-height: 3rem;
            }
        }

        #{$this}__teaser {
            font-size: 1.8rem;
            line-height: 3rem;
        }
    }
}
