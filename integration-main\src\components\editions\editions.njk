{% from 'views/utils/constants.njk' import kGlobalLinks %}
{% from 'views/core-components/section.njk' import Section %}
{% from 'views/core-components/carousel.njk' import CarouselWrapper %}
{% from 'views/core-components/title.njk' import TitlePrimary %}
{% import 'views/core-components/secondary.njk' as Secondary %}
{% from 'views/core-components/link.njk' import Link %}
{% from 'views/core-components/image.njk' import Image %}
{% from 'views/core-components/icon.njk' import Icon %}

{#
    EditionsItem template.
#}
{% macro EditionItem(
    imageSizes = ['281x398'],
    category = 'Lorem ipsum',
    title = 'Magazine de la ville',
    subtitle = 'Septembre 2099 - N°999',
    teaser = 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore',
    documents = range(1, 3) | random
) %}
    <article class="edition-item">
        <div class="edition-item__image-wrapper">
            {{ Image({
                sizes: imageSizes,
                className: 'edition-item__image',
                type: 'no-image' if range(0,2) | random else 'default',
                serviceID: range(50) | random
            }) }}
        </div>
        <div class="edition-item__content">
            <h3 class="item-title is-large edition-item__title">
                {%- if category %}
                    <span class="theme edition-item__category">{{ category }}</span>
                    <span class="sr-only">:</span>
                {%- endif %}
                <a href="{{ kGlobalLinks.singlePublications }}" class="edition-item__title-link">
                    <span class="underline">{{ title }}</span>
                </a>
                {%- if subtitle %}
                    <span class="edition-item__subtitle">{{ subtitle }}</span>
                {%- endif -%}
            </h3>
            {%- if teaser %}
                <p class="item-teaser edition-item__teaser">{{ teaser }}</p>
            {%- endif -%}
            <p class="publication edition-item__publication">
                <span>Publié le</span>
                <time datetime="{{ datetime }}">27/11/2021</time>
            </p>

            {%- if documents === 1 -%}
                <div class="edition-item__actions">
                    {{ Secondary.DocumentActions() }}
                </div>
            {% else %}
                <p class="edition-item__document">
                    <span class="edition-item__number">{{ documents }} documents</span>
                </p>
            {%- endif -%}

        </div>
    </article>
{% endmacro %}

{#
    EditionsCarousel template.
#}
{% macro EditionsCarousel(
    ariaLabelCarousel = 'En kiosque',
    itemsCount = 3
) %}
    {% call CarouselWrapper({
        wrapperClassName: 'editions-block',
        wrapperModifier: 'no-animate',
        jsClassName: 'js-swiper-coverflow',
        wrapperAttrs: {
            'aria-label': ariaLabelCarousel
        },
        actions: false,
        arrows: {
            next: {
                icon: 'far fa-long-arrow-right'
            },
            prev: {
                icon: 'far fa-long-arrow-left'
            }
        }
    }) %}
        {% for item in range(itemsCount) %}
            <div class="editions__item swiper-item" aria-label="Lorem ipsum Magazine de la ville Septembre 2099 - N°999" role="group">
                {{ EditionItem() }}
            </div>
        {% endfor %}
    {% endcall %}
{% endmacro %}

{#
    EditionsHome template.
    Template for events on home page.
    @param {string} titleText - section title
    @param {number} itemsCount - count of events
    @param {boolean} moreButton - insert more link
    @param {boolean} proposerButton - insert proposer link
#}
{% macro EditionsHome(
    titleText = 'À consulter',
    moreButton = true
) %}
    {% call Section(className = 'editions', container = 'editions__container') %}
        <div class="section__title">
            {{ TitlePrimary(
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            {{ EditionsCarousel(
                ariaLabelCarousel = titleText
            ) }}
        </div>
        {% if moreButton %}
            <div class="section__more-links">
                {% if moreButton %}
                    {{ Link(
                        href = kGlobalLinks.listPublications,
                        text = 'Toutes les publications',
                        className = 'btn is-primary is-small',
                        icon = 'far fa-arrow-right'
                    ) }}
                {% endif %}
            </div>
        {% endif %}
    {% endcall %}
{% endmacro %}
