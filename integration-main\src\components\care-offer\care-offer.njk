{% from Helpers.core import button, form, tabs, title with context %}

<section class="care-offer">
    <div class="care-offer__wrapper">
        {{ title(
            className = 'care-offer__title',
            text = 'Vous recherchez'
        ) }}

        {% call tabs.setTabs(withAccordion = true, modifier = 'care-offer__tabs') %}
            {{ tabs.setTabsList(
                data = [
                    {
                        text: 'Un service'
                    },
                    {
                        text: 'Un medicin'
                    },
                    {
                        text: 'Une consultation'
                    }
                ]
            ) }}
            {% call button(className = 'btn is-secondary is-small js-tabs-panel-close care-offer__tabs-close', icon = 'far fa-times') %}
                <span class="ghost">Fermer l'onglet</span>
            {% endcall %}
            {% call tabs.setTabsPanel(withAccordionToggle = true) %}
                {%- call form.setForm(legend = false) -%}
                    <fieldset class="form__fieldset">
                        <legend class="form__legend">En recherchant par</legend>

                        {% call form.setGroup() %}
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Son nom officiel'
                                ) }}
                            </div>
                            <div class="col-xs-12 is-inline">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Les specialites qu\'il traite'
                                ) }}
                            </div>
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Les maladies qu\'il traite'
                                ) }}
                            </div>
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Les organes qu\'il traite'
                                ) }}
                            </div>
                        {% endcall %}
                    </fieldset>
                    {% call form.setActions(className = 'is-right') %}
                        {{ button(className = 'btn is-primary', type = 'submit', text = 'submit') }}
                    {% endcall %}
                {% endcall %}
            {% endcall %}
            {% call tabs.setTabsPanel(withAccordionToggle = true) %}
                {%- call form.setForm(legend = false) -%}
                    <fieldset class="form__fieldset">
                        <legend class="form__legend">En recherchant par</legend>

                        {% call form.setGroup() %}
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'text',
                                    label = 'Son nom/prenom'
                                ) }}
                            </div>
                            <div class="col-xs-12 is-inline">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Son service d\'affectation'
                                ) }}
                            </div>
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Les maladies qu\'il traite'
                                ) }}
                            </div>
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Les organes qu\'il traite'
                                ) }}
                            </div>
                        {% endcall %}
                    </fieldset>
                    {% call form.setActions(className = 'is-right') %}
                        {{ button(className = 'btn is-primary', type = 'submit', text = 'submit') }}
                    {% endcall %}
                {% endcall %}
            {% endcall %}
            {% call tabs.setTabsPanel(withAccordionToggle = true) %}
                {%- call form.setForm(legend = false) -%}
                    <fieldset class="form__fieldset">
                        <legend class="form__legend">En recherchant par</legend>

                        {% call form.setGroup() %}
                            <div class="col-xs-12 is-inline">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Son service d\'affectation'
                                ) }}
                            </div>
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Les maladies qu\'il traite'
                                ) }}
                            </div>
                            <div class="col-xs-12">
                                {{ form.setField(
                                    type = 'select',
                                    label = 'Les organes qu\'il traite'
                                ) }}
                            </div>
                        {% endcall %}
                    </fieldset>
                    {% call form.setActions(className = 'is-right') %}
                        {{ button(className = 'btn is-primary', type = 'submit', text = 'submit') }}
                    {% endcall %}
                {% endcall %}
            {% endcall %}
        {% endcall %}

        <div class="care-offer__info">
            <p class="care-offer__text">
                Vous désirez découvrir notre offre de soins et nos services ?<br>
                Cliquez sur un des onglets afin de trouver une réponse à votre besoin
            <p>
        </div>
    </div>
</section>
