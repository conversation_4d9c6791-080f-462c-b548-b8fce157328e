{%- from 'views/utils/constants.njk' import kGlobalLinks -%}
{%- import 'components/heading/heading.njk' as Heading -%}
{%- from 'views/core-components/link.njk' import Link -%}
{%- from 'components/popup/popup.njk' import Popup -%}
{%- import 'views/core-components/filter.njk' as Filter -%}
{%- import 'views/core-components/form.njk' as Form -%}
{%- from 'components/page-list/page-list.njk' import PageList -%}
{%- from 'components/faq/faq.njk' import IdeasBoxList -%}

{% set pageConfig = {
    category: 'LOREM IPSUM',
    title: 'PARTAGEZ VOS IDÉES',
    pageImage: 'random',
    hasFilter: true,
    bundle: 'list',
    bodyClass: 'is-list-ideas-box'
} %}

{% extends 'views/layouts/base.njk' %}

{%- block heading -%}
    {% call Heading.Heading(overwriteWrapper = true) %}
        {% call Heading.HeadingContent() %}
            {{ Heading.HeadingPartials('titleWithCategory', config.title, false, config.category) }}
        {% endcall %}
    {% endcall %}
{%- endblock -%}

{%- block filterNumberBlock -%}
    <div class="filter-wrapper">
        {% call Filter.FilterWrapper(legend = false, ariaLabelText = false) %}
            {% call Filter.FieldsWrapper() %}
                {% call Filter.FieldsGroup() %}
                    {% call Filter.FilterField('col-lg-4 col-md-12') %}
                        {{ Form.FormField(type= 'autocomplete', label = 'Mots-clés', placeholder = 'mots-clés..') }}
                    {% endcall %}

                    {% call Filter.FilterField('col-lg-4 col-md-12') %}
                        {{ Form.Multiselect(legend = 'Thématiques' , placeholder = '-Sélectionner-') }}
                    {% endcall %}

                    {% call Filter.FilterField('col-lg-4 col-md-12') %}
                        {{ Form.Multiselect(legend = 'Tags' , placeholder = '-Sélectionner-') }}
                    {% endcall %}
                {% endcall %}
                    {{ Filter.ButtonsWrapper() }}
            {% endcall %}
        {% endcall %}
    </div>
{%- endblock -%}

{% block content %}
    {% call PageList(
        useFilterButton = false,
        useButtons = true,
        itemsCount = 12,
        list = IdeasBoxList(),
        pagerType = 'default'
    ) %}
        {{ Link(
            className = 'btn is-small',
            text = 'proposer une idée',
            icon = 'far fa-lightbulb'
        ) }}
    {% endcall %}
{% endblock %}

{% block sidebar %}{% endblock %}

{% block hiddenElements %}
    {% call Popup(
        id = 'filters-popup',
        modifier = 'is-filters',
        closeBtn = {
            modifier: 'is-filters-close-btn',
            ghostText: true,
            tooltip: 'Fermer les filtres'
        }
    ) %}
        {% call Filter.FilterWrapper(
            legend = 'Filtrer les questions par :',
            ariaLabelText = 'Filtrer la foire aux questions'
        ) %}
            {% call Filter.FieldsWrapper() %}
                {% call Filter.FieldsGroup() %}
                    {% call Filter.FilterField('col-md-6') %}
                        {{ Form.FormField(label = 'Mots clés') }}
                    {% endcall %}

                    {% call Filter.FilterField('col-md-6') %}
                        {{ Form.Multiselect(legend = 'Thématiques') }}
                    {% endcall %}
                {% endcall %}
            {% endcall %}

            {{ Filter.ButtonsWrapper() }}
        {% endcall %}
    {% endcall %}
{% endblock %}

{% block contentBottom %}{% endblock %}
