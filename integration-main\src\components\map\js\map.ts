/* eslint-disable no-shadow */
import Map from 'ol/Map';
import View from 'ol/View';
import Feature from 'ol/Feature';
import { Cluster, Vector as VectorSource } from 'ol/source';
import { Layer, Tile as TileLayer } from 'ol/layer';
import Collection from 'ol/Collection';
import ScaleLine from 'ol/control/ScaleLine';
import { Attribution } from 'ol/control';
import { createEmpty, extend } from 'ol/extent';
import { defaults as defaultInteractions, MouseWheelZoom } from 'ol/interaction';
import { transform } from 'ol/proj';
import Geolocation from 'ol/Geolocation';
import axios from 'axios';

import { addClass, hasClass, removeClass } from '@core/utils/class.utils';
import { extendDefaults } from '@core/utils/object.utils';
import { convertPrimitiveKeys } from '@core/utils/converters.utils';
import { getUniqId } from '@core/utils/common.utils';
import { callIfFunction } from '@core/utils/function.utils';
import { parseUrl } from './utils/url.utils';
import DB from '@core/core-base/stratis-db';

import StratisMapLayer from './components/map-layer';
import StratisMapMarker from './components/map-marker';
import StratisMapOverlay from './components/map-overlay';
import StratisMapPlugin from './plugins/map-plugin.abstract';

import { StratisMapEvents, StratisMapOptions, MapRequestParams } from './models/map.model';
import { LayerTypes } from './enums/map.enum';
import { Coordinates } from './types/map.types';
import Point from 'ol/geom/Point';
import { getFirstFocusableElement } from '@core/utils/a11y.utils';
import { setAttributes } from '@core/utils/attr.utils';

import { tabFocus } from 'ally.js/maintain/_maintain';
import { key } from 'ally.js/when/_when';
import { StringBool } from '@core/interfaces/base-types';


export default class StratisMap {
    // Map instance id.
    private readonly id: string = getUniqId();

    // Map events
    private readonly events: StratisMapEvents = {
        popupShow: 'mapPopupShow',
        popupHide: 'mapPopupHide',
        mapReady: 'mapReady',
    };

    // Map options.
    private options: StratisMapOptions = {
        contextEl: '.js-map-context',
        popupEl: '.js-map-popup-context',
        // Map general props
        zoomToExtent: true,
        zoomToContentOnLoad: true,
        zoomToExtentOnMarkerClick: true,
        zoomToExtentOnClusterClick: true,
        zoomToContentOnPopupClose: true,
        showPopupForSingleMarker: true,
        disableFocusOnPopup: false,
        disableMarkerClick: false,
        // Aria label for canvas element
        canvasAriaLabel: 'Carte interactive',
        // Map center by default
        defaultLat: 43.4803088918,
        defaultLon: -1.460096987,
        // Map zoom settings
        defaultZoom: 13,
        minZoom: 0,
        maxZoom: 20,
        // Zoom controls settings
        showZoomButtons: true,
        zoomInTipLabel: 'Zoom +',
        zoomOutTipLabel: 'Zoom &minus',
        // POI default icons
        defaultMarker: 'images/map/map-marker.svg',
        selectedMarker: 'images/map/map-marker.svg',
        geopositionMarker: 'images/map/map-marker-geoposition.png',
        markerColor: '#17145c',
        selectedMarkerColor: '#26c3ec',
        // Interactions
        mouseWheelZoom: true,
        // Padding for map view
        padding: [50, 50, 50, 50],
        // Wikimedia example: https://maps.wikimedia.org/osm-intl/{z}/{x}/{y}.png
        OSMService: '',
        // Params for BE devs
        BERequestUrl: `${window.location.origin}/?tx_news_pi1[overwriteDemand][idList]={uid}&type={type}`,
        useGeoportal: false,
        geoportalKey: 'pratique',
        bingMapsKey: 'Aj6XtE1Q1rIvehmjn2Rh1LR2qvMGZ-8vPS9Hn3jCeUiToM77JFnf-kFRzyMELDol',
        selectors: {
            searchResults: '.map-search-result',
            templateColumn: '.map-template__column',
            filterTemplateColumn: '#map-filter-column',
            searchToggle: '.map-search-toggle',
            filterToggle: '.map-header__filters-button',
            zoomControll: '.ol-zoom',
            canvasLayer: '.ol-layer canvas',
        },
        classList: {
            hidden: 'is-hidden',
            visible: 'is-visible',
            loading: 'is-loading',
            animated: 'is-animated',
            marker: 'js-map-marker',
            layer: 'js-map-layer',
            cover: 'js-map-cover',
            overlay: 'js-map-overlay',
            popup: 'js-map-popup',
            popupShowAction: 'js-map-popup-show',
            popupHideAction: 'js-map-popup-hide',
        },
        defaultLayers: [
            {
                layerName: 'aero',
                isVisible: false,
                type: LayerTypes.bing,
                key: 'Aj6XtE1Q1rIvehmjn2Rh1LR2qvMGZ-8vPS9Hn3jCeUiToM77JFnf-kFRzyMELDol',
                imagerySet: 'AerialWithLabelsOnDemand',
                attributions: [
                    '© <a href="https://www.microsoft.com/en-us/maps/product/print-rights" target="_blank">BingMaps</a> contributeurs.',
                ],
            },
            {
                layerName: 'osm',
                isVisible: true,
                type: LayerTypes.osm,
                attributions: [
                    '© <a href="https://www.openstreetmap.org/copyright" target="_blank">OpenStreetMap</a> contributeurs.',
                ],
            },
            {
                layerName: 'aero',
                isVisible: false,
                type: LayerTypes.geoportal,
                url: 'https://wxs.ign.fr/[key]/geoportail/wmts',
                layer: 'ORTHOIMAGERY.ORTHOPHOTOS',
                matrixSet: 'PM',
                format: 'image/jpeg',
                projection: 'EPSG:3857',
                style: 'normal',
            },
            {
                layerName: 'osm',
                isVisible: true,
                type: LayerTypes.geoportal,
                url: 'https://wxs.ign.fr/[key]/geoportail/wmts',
                layer: 'GEOGRAPHICALGRIDSYSTEMS.PLANIGNV2',
                matrixSet: 'PM',
                format: 'image/png',
                projection: 'EPSG:3857',
                style: 'normal',
            },
        ],
        overlay: {
            offset: [0, -25],
            clusterOffset: [0, -25],
            positioning: 'bottom-center',
            stopEvent: false,
        },
        cluster: {
            outerColor: '#17145c',
            innerColor: '#17145c',
            radius: 25,
            text: '',
            font: 'normal 16px Poppins',
            fontColor: '#fff',
            strokeWidth: 0,
        },
        selectedMarkerBorder: {
            color: 'rgba(38, 195, 236, 0.5)',
            radius: 40,
            width: 8,
        },
        route: {
            color: 'rgba(202, 0, 10, 1)',
            width: 4,
        },
        // Callbacks
        beforeInit: () => null,
        afterInit: () => null,
        onMarkerClick: () => null,
        onDataFetch: () => null,
        beforeUpdate: () => null,
        afterUpdate: () => null,
    };

    // Openlayers map instance
    private olInstance: Map | null = null;

    // Map selector
    private readonly selector: HTMLElement | string;

    // Map render container (HTMLElement).
    private element: HTMLElement | null = null;

    // Map popup root element (HTMLElement)
    private popupEl: HTMLElement | null = null;

    // Map root context element (HTMLelement)
    private contextEl: HTMLElement | null = null;

    // Search results (HTMLElement)
    private searchResults!: HTMLElement[];

    // ZoomControll Element
    private zoomControll!: HTMLElement;

    // Canvas Layer Element
    private canvasLayer: HTMLElement[] | null = null;

    // Filter column
    private filterColumn!: HTMLElement;

    // Search toggle
    private searchToggle!: HTMLElement;

    // Filter toggle
    private filterToggle!: HTMLElement;

    // Map initialize state
    private created = false;

    // Map render state
    private isReady = false;

    // Map layers loading queue
    private readonly layersQueue: string[] = [];

    // Map background layers collection
    private readonly defaultLayers: StratisMapLayer[] = [];

    // Map layers collection
    private layers: StratisMapLayer[] = [];

    // Map cluster layer
    public clusterLayer: StratisMapLayer | null = null;

    // Map markers collection
    private markers: StratisMapMarker[] = [];

    // Map active feature (marker for example)
    private activeMarker: Feature<Point> | null = null;

    // Openlayers Map Features array.
    private readonly olFeatures: any[] = [];

    // Map controls collection
    private readonly controls: Collection<any> = new Collection();

    // Map scroll state
    private isScrollActivated = true;

    // A11y handlers
    protected $keyHandler;
    protected $focusHandler;

    // Map interactions collection
    private readonly interactions: Collection<any> = defaultInteractions();

    // Map MouseWheel interaction
    private mouseWheelInteraction: MouseWheelZoom | null = null;

    // MouseWheelHandler bindinig
    private readonly $mouseWheelHandler = this.mouseWheelHandler.bind(this);

    // Map markers default overlay.
    private readonly markerOverlay: StratisMapOverlay = new StratisMapOverlay({
        positioning: this.options.overlay?.positioning,
        stopEvent: this.options.overlay?.stopEvent,
    });

    // Map overlays collection
    private readonly overlays: Collection<any> = new Collection();

    // Map marker overlay handler binding
    private readonly $markerOverlayHandler = this.markerOverlayHandler.bind(this);

    // Resize handler binding
    private readonly $resizeHandler = this.resizeHandler.bind(this);

    // Features click handler
    private readonly $featureHandler = this.featureHandler.bind(this);

    // Set custom attrs to the canvas layers
    private readonly $canvasHandler = this.canvasHandler.bind(this);

    // ZoomControllButton click handler
    private readonly $handleZoomControlButtonClick = this.handleZoomControlButtonClick.bind(this);

    // Popup handler
    private readonly $popupHandler = this.popupHandler.bind(this);

    // Route request url
    private readonly routeRequestUrl = 'https://itineraire.ign.fr/simple/1.0.0/route?resource=bdtopo-osrm&start={start}&end={end}&profile=car&optimization=fastest&getSteps=true&getBbox=true&distanceUnit=kilometer&timeUnit=hour&crs=EPSG%3A4326';

    // Route fallback link if we have troubles with route construction process.
    private routeFallbackLink = '';

    // Active route layer
    private activeRoute: StratisMapLayer | null = null;

    private markerFocusPadding: number[] = [50, 120, 50, 50];

    /**
     * Throw map error.
     * @param message
     */
    public static emitError(message: string): void {
        throw new Error(message);
    }

    /**
     * Get element
     */
    public static getElement(selector: HTMLElement | string, rootElement: null | HTMLElement, required = false): HTMLElement | null | never {
        const rootEl = rootElement || document;
        let element;

        if (typeof selector === 'string') {
            element = rootEl.querySelector(selector);
        } else if (selector instanceof HTMLElement) {
            element = selector;
        } else if (required) {
            StratisMap.emitError(`Selector ${selector}, must be a query string or HTMLElement`);
        }

        if (!element && required) {
            StratisMap.emitError(`Can't find map element with selector ${selector}`);
        }

        return element;
    }

    /**
     * Extract layers from StratisMapLayers array.
     * @param layers
     */
    public static extractOlLayers(layers): any {
        return layers.map(layer => layer.getOlLayer());
    }

    /**
     * Extract features from Layer.
     * @param layer
     */
    public static extractOlFeatures(layer): any {
        const source = layer.getSource();

        if (
            source instanceof VectorSource
            && !(source instanceof Cluster)
        ) {
            return source.getFeatures();
        }

        if (source instanceof Cluster) {
            return source.getSource()!.getFeatures();
        }

        return [];
    }

    constructor(element: HTMLElement | string, options?: Partial<StratisMapOptions>) {
        this.selector = element;

        if (!this.created) {
            this.options = Object.assign({}, this.options, options);
            this.init();
        }
    }

    /**
     * Recalculate map dimentions.
     */
    public updateSize(delay?: number): void {
        setTimeout(() => {
            this.olInstance!.updateSize();
        }, delay || 100);
    }

    /**
     * Zoom to passed features.
     */
    public zoomToExtent(features: Feature<Point>[] | Feature<Point>, duration = 200, cb?: Function): void {
        const { zoomToExtent, maxZoom } = this.options;

        if (!zoomToExtent) {
            return;
        }

        const featuresArray: Feature<Point>[] = Array.isArray(features) ? features : [features];
        const view = this.olInstance!.getView();

        const extent = featuresArray.reduce((acc, feature) => {
            if (feature instanceof Layer) {
                return extend(acc, feature.getSource().getExtent());
            }

            if (feature instanceof Feature) {
                return extend(acc, feature.getGeometry()!.getExtent());
            }

            return acc;
        }, createEmpty());
        if (extent.indexOf(Infinity) < 0) {
            view.fit(extent, {
                size: this.olInstance!.getSize(),
                // constrainResolution: false,
                maxZoom,
                padding: this.markerFocusPadding,
                callback: () => {
                    callIfFunction(cb);
                    this.updateSize();
                },
                duration,
            });
        }
    }


    /**
 * Zoom to a specific feature and adjust the zoom level by +2.
 */
    public zoomToFeatureWithZoomLevel(features: Feature<Point>[] | Feature<Point>, duration = 200, cb?: Function): void {
        const { maxZoom } = this.options;

        const featuresArray: Feature<Point>[] = Array.isArray(features) ? features : [features];
        const view = this.olInstance!.getView();

        // Get the current zoom level
        const currentZoom = view.getZoom();

        const newZoomLevel = currentZoom + 5;

        // Create the extent of the features
        const extent = featuresArray.reduce((acc, feature) => {
            if (feature instanceof Feature) {
                return extend(acc, feature.getGeometry()!.getExtent());
            }
            return acc;
        }, createEmpty());

        // Ensure extent is valid (not infinite)
        if (extent.indexOf(Infinity) < 0) {
            view.fit(extent, {
                size: this.olInstance!.getSize(),
                maxZoom: Math.min(newZoomLevel, maxZoom),
                padding: this.markerFocusPadding,
                callback: () => {
                    callIfFunction(cb);
                    this.updateSize();
                },
                duration,
            });
        }
    }


    /**
     * Zoom on all loaded features and layers.
     * @param {Number} duration - animation duration.
     */
    public zoomOnContent(duration = 300): void {
        if (!this.olFeatures.length && this.olInstance) {
            this.olInstance.getLayers().forEach((layer: any) => {
                if (layer.get('area')) {
                    this.olFeatures.push(layer);
                } else {
                    this.olFeatures.push(...StratisMap.extractOlFeatures(layer));
                }
            });
        }

        this.zoomToExtent(this.olFeatures, duration);
    }

    /**
     * Update map fit and set padding to it if it needs
     * @param {Boolean} zoomOnContent - zoom on all features or not
     */
    public updateBounds(zoomOnContent = false): void {
        if (zoomOnContent) {
            this.zoomOnContent();
        }
    }

    /**
     * Change map background layer.
     * @param type
     */
    public changeBackgoundLayer(type: string): void {
        this.defaultLayers.forEach(layer => {
            layer.getOlLayer().setVisible(layer.getProp('layerName') === type);
        });
    }

    /**
     * Get map main context.
     */
    public getContextElement(): HTMLElement {
        return this.contextEl!;
    }

    /**
     * Connect plugin to map instance.
     * @param plugin
     */
    public addPlugin(plugin: StratisMapPlugin): void {
        try {
            plugin.connect(this);
        } catch (err) {
            console.error((err as any).message);
        }
    }

    public setCoords(lon: number, lat: number): number[] {
        return transform([lon, lat], 'EPSG:4326', 'EPSG:3857');
    }

    /**
     * Find feature by uid in features.
     */
    public findMarker(uid): Feature<Point> | null {
        return this.olFeatures.find(feature => feature.get('uid') === uid);
    }

    /**
     * Select marker bu uid
     */
    public selectMarker(uid: string, zoomOnMarker = true): Feature<Point> | null {
        const feature = this.findMarker(uid);

        if (feature) {
            feature.set('selected', true);
            this.activeMarker = feature;

            if (zoomOnMarker) {
                this.zoomToExtent(feature);
            }
        }

        return this.activeMarker;
    }

    /**
     * Deselect all markers.
     */
    public resetMarkers(zoomOnContent = true): void {
        if (this.activeMarker) {
            this.activeMarker.set('selected', false);
            this.activeMarker = null;
        }

        if (zoomOnContent) {
            this.zoomOnContent();
        }
    }

    /**
     * Get map HTMLElement container
     */
    public getElement(): HTMLElement {
        return this.element as HTMLElement;
    }

    /**
     * Get Openlayers Map instance
     */
    public getOLInstance(): Map {
        return this.olInstance!;
    }

    /**
     * Get Openlayers features
     */
    public getOLFeatures(): Feature<Point>[] {
        return this.olFeatures;
    }

    /**
     * Get Openlayers layers.
     */
    public getOLLayers(): Layer<any>[] {
        return StratisMap.extractOlLayers(this.layers);
    }

    /**
     * Update map data from DOM.
     * @description
     * JSON:
     * {
     *    layers: [ {object with layer settings}, {object with layer settings} ],
     *    markers: [ {object with marker settings}, {object with marker settings} ]
     * }
     */
    public async updateWithJSON(featuresJSON: { [key: string]: any } = {}): Promise<void> {
        const layers: StratisMapLayer[] = [];
        const markers: StratisMapMarker[] = [];
        const { beforeUpdate } = this.options;

        if (featuresJSON.layers) {
            featuresJSON.layers.forEach(layerOpts => {
                layers.push(new StratisMapLayer(layerOpts)!);
            });
        }

        if (featuresJSON.markers) {
            featuresJSON.markers.forEach(markerOpts => {
                markers.push(new StratisMapMarker(markerOpts));
            });
        }

        beforeUpdate(markers, layers);
        await this.runUpdate(markers, layers);
    }

    /**
     * Update map data from DOM.
     * @param {HTMLElement[]} featuresArray - array of DOM elements.
     */
    public async updateWithDOM(featuresArray: HTMLElement[]): Promise<void> {
        const layers: StratisMapLayer[] = [];
        const markers: StratisMapMarker[] = [];
        const { classList, beforeUpdate } = this.options;

        if (featuresArray.length) {
            featuresArray.forEach(feature => {
                if (hasClass(feature, classList.layer)) {
                    layers.push(StratisMapLayer.createFromHTML(feature)!);
                } else if (hasClass(feature, classList.marker)) {
                    markers.push(StratisMapMarker.createFromHTML(feature)!);
                }
            });
        }

        beforeUpdate(markers, layers);
        await this.runUpdate(markers, layers);
    }

    /**
     * Show map popup element.
     * @param {object} params - popup params (uid, url, type).
     */
    public showPopup(params: MapRequestParams[] = []): void {
        // Reset markers without zooming.
        this.resetMarkers(false);
        const paramsForRequest = this.getUidsFromParams(params);
        const isMapPage = hasClass(document.body, 'map-page');

        if (!this.popupEl) {
            StratisMap.emitError(`Can't find popup context element, ${this.popupEl}`);
        }

        if (paramsForRequest.manualPoint) {
            setTimeout(() => this.showManualPopup(paramsForRequest.uid), 150);
        } else {
            this.showAsyncPopup(paramsForRequest);
        }
        this.popupEl?.setAttribute('id', paramsForRequest.uid);
        this.clearResultsState();
        this.setActiveStateToResultElement(paramsForRequest.uid);
        addClass(this.contextEl!, 'is-popup-open');
        this.$focusHandler = tabFocus({
            context: isMapPage ? this.popupEl : document.body,
        });

        if (isMapPage) {
            this.$keyHandler = key({
                context: document.body,
                escape: () => {
                    this.hidePopup();
                    const prevElement = this.searchResults.find(item => item.getAttribute('data-uid') === paramsForRequest.uid);
                    prevElement?.focus();
                },
            });
        }
    }
    /**
     * Hide map popup element.
     */
    public hidePopup(): void {
        const { classList, zoomToContentOnPopupClose } = this.options;
        this.resetMarkers();

        if (!this.popupEl) {
            StratisMap.emitError(`Can't find popup context element, ${this.popupEl}`);
        }

        const uid = this.popupEl!.getAttribute('id')!;
        addClass(this.popupEl!, classList.hidden);
        this.resetActiveRoute();

        if (zoomToContentOnPopupClose) {
            this.zoomOnContent();
        }

        this.contextEl!.dispatchEvent(this.events.popupHide);
        removeClass(this.contextEl!, 'is-popup-open');
        this.removeActiveStateFromResultElement(uid);

        if (this.$focusHandler) {
            this.$focusHandler.disengage();
        }
    }

    /**
     * Add marker to cluster vector source
     * @param marker - marker to be added
     */
    public addMarker(marker: StratisMapMarker): void {
        const olFeatures = this.getOLFeatures();
        const olFeature = marker.getOlFeature();
        const clusterSource = this.clusterLayer!.getSource();
        const vectorSource = clusterSource.getSource();

        olFeatures.push(olFeature!);
        this.markers.push(marker);
        vectorSource.addFeature(olFeature);
        clusterSource.refresh();
    }

    /**
     * Remove marker from cluster vector source
     * @param uid - string
     */
    public removeMarker(uid: string): void {
        const olFeatures = [...this.getOLFeatures()];
        const clusterSource = this.clusterLayer!.getSource();
        const vectorSource = clusterSource.getSource();
        const featureToRemove = olFeatures.find(item => item.get('uid') === uid);

        vectorSource.removeFeature(featureToRemove);
        clusterSource.refresh();
    }

    /**
     * Add custom layer to the map
     */
    public addLayer(layer: StratisMapLayer): void {
        this.layers.push(layer);
        this.layersQueue.push(layer.getProp('uid'));
        this.olInstance!.addLayer(layer.getOlLayer());
    }

    /**
     * Remove custom layer from the map
     */
    public removeLayer(layer: StratisMapLayer): void {
        this.layers = this.layers.filter(item => item.getProp('uid') !== layer.getProp('uid'));
        this.olInstance!.removeLayer(layer.getOlLayer());
    }

    /**
     * Toggle visibility of marker
     * @param el - marker to manipulate
     * @param value - boolean visible
     */
    public showHideMarker(el, value): void {
        el.set('hidden', value);
    }

    /**
     * Toggle visibility of layer
     * @param el - layer to manipulate
     * @param value - boolean visible
     */
    public showHideLayer(el, value): void {
        el.set('visible', value);
    }

    /**
     * Filter markers based on search criteria
     * @param searchTerm - string to search for in marker properties
     * @param searchFields - array of property names to search in (default: ['title', 'category', 'description'])
     */
    public filterMarkers(searchTerm: string, searchFields: string[] = ['title', 'category', 'description']): void {
        if (!searchTerm || searchTerm.trim() === '') {
            this.resetFilters();
            return;
        }

        const normalizedSearchTerm = searchTerm.toLowerCase().trim();
        const clusterSource = this.getClusterLayerSource();
        const vectorSource = clusterSource?.getSource();

        if (!vectorSource) {
            return;
        }

        const features = vectorSource.getFeatures();
        let visibleCount = 0;

        // Check if the search term contains comma-separated IDs
        const ids = normalizedSearchTerm.split(',').map(id => id.trim());

        features.forEach(feature => {
            let isVisible = false;
            // I the search term contains comma-separated IDs, filter by those IDs
            if (ids.length > 1 || !isNaN(Number(ids[0]))) {
                const properties = feature.getProperties();
                const featureId = properties['uid'];
                isVisible = ids.includes(featureId);
            } else {
                // Otherwise, perform a search based on the search fields
                isVisible = this.featureMatchesSearchCriteria(feature, normalizedSearchTerm, searchFields);
            }

            feature.set('filtered', !isVisible);
            feature.set('hidden', !isVisible);

            if (isVisible) {
                visibleCount++;
            }
        });

        // Refresh the cluster to update visibility
        clusterSource?.refresh();

        // Update search results if they exist
        this.updateSearchResults(normalizedSearchTerm, visibleCount);

        // Zoom to visible markers if any
        if (visibleCount > 0) {
            this.zoomToVisibleMarkers();
        }

    }

    /**
     * Check if a marker matches the search criteria
     * @param feature - OpenLayers feature to check
     * @param searchTerm - normalized search term
     * @param searchFields - fields to search in
     */
    public getClusterLayerSource(): any {
        return this.clusterLayer?.getSource();
    }

    // Public method to check if a feature matches search criteria
    public featureMatchesSearchCriteria(feature: any, searchTerm: string, searchFields: string[]): boolean {
        const searchTermLower = searchTerm.toLowerCase();
        return searchFields.some(field => {
            const fieldValue = feature.get(field)?.toString().toLowerCase();
            return fieldValue && fieldValue.includes(searchTermLower);
        });
    }

    /**
     * Reset all filters and show all markers
     */
    public resetFilters(): void {
        const clusterSource = this.clusterLayer?.getSource();
        const vectorSource = clusterSource?.getSource();

        if (!vectorSource) {
            return;
        }

        const features = vectorSource.getFeatures();
        features.forEach((feature: Feature<Point>) => {
            feature.set('filtered', false);
            feature.set('hidden', false);
        });

        // Refresh the cluster
        clusterSource?.refresh();

        // Update search results
        this.updateSearchResults('', features.length);

        // Zoom to all content
        this.zoomOnContent();
    }

    /**
     * Update search results display
     * @param searchTerm - current search term
     * @param visibleCount - number of visible markers
     */
    private updateSearchResults(searchTerm: string, visibleCount: number): void {
        // Update results count if element exists
        const resultsCountElement = document.querySelector('.map-search-results-count');
        if (resultsCountElement) {
            resultsCountElement.textContent = `${visibleCount} résultat${visibleCount !== 1 ? 's' : ''}`;
        }

        // Update search results list
        this.updateSearchResultsList(searchTerm);
    }

    /**
     * Update the search results list based on visible markers
     * @param searchTerm - current search term for highlighting
     */
    private updateSearchResultsList(searchTerm: string): void {
        const searchResultsList = document.querySelector('.map-search-results__list');
        if (!searchResultsList) {
            return;
        }

        // Clear existing results
        searchResultsList.innerHTML = '';

        const clusterSource = this.clusterLayer?.getSource();
        const vectorSource = clusterSource?.getSource();

        if (!vectorSource) {
            return;
        }

        const visibleFeatures = vectorSource.getFeatures().filter((feature: Feature<Point>) => !feature.get('hidden'));

        visibleFeatures.forEach((feature: Feature<Point>, index: number) => {
            const properties = feature.getProperties();
            const listItem = this.createSearchResultItem(properties, index + 1, searchTerm);
            searchResultsList.appendChild(listItem);
        });
    }

    /**
     * Create a search result list item
     * @param properties - marker properties
     * @param index - item index
     * @param searchTerm - search term for highlighting
     */
    private createSearchResultItem(properties: any, index: number, searchTerm: string): HTMLElement {
        const listItem = document.createElement('li');
        listItem.className = 'map-search-results__item';

        const button = document.createElement('button');
        button.setAttribute('data-uid', properties.uid || index.toString());
        button.setAttribute('aria-controls', properties.uid || index.toString());
        button.setAttribute('type', 'button');
        button.className = 'map-search-result js-map-popup-show';

        const wrap = document.createElement('span');
        wrap.className = 'map-search-result__wrap';

        const content = document.createElement('span');
        content.className = 'map-search-result__content';

        const title = document.createElement('span');
        title.className = 'map-search-result__title';

        // Add category if available
        if (properties.category) {
            const category = document.createElement('span');
            category.className = 'map-search-result__category';
            category.textContent = this.highlightSearchTerm(properties.category, searchTerm);
            title.appendChild(category);

            const separator = document.createElement('span');
            separator.className = 'sr-only';
            separator.textContent = ':';
            title.appendChild(separator);
        }

        // Add title/name
        const titleText = document.createElement('span');
        titleText.textContent = this.highlightSearchTerm(properties.title || properties.name || 'Unnamed', searchTerm);
        title.appendChild(titleText);

        content.appendChild(title);
        wrap.appendChild(content);

        // Add marker icon
        const icon = document.createElement('span');
        icon.className = 'map-search-result__image';
        icon.setAttribute('aria-hidden', 'true');
        icon.innerHTML = '<svg width="30" height="37" viewBox="0 0 30 37"><use href="#map-marker-results"></use></svg>';
        wrap.appendChild(icon);

        button.appendChild(wrap);
        listItem.appendChild(button);

        return listItem;
    }

    /**
     * Highlight search term in text
     * @param text - text to highlight in
     * @param searchTerm - term to highlight
     */
    private highlightSearchTerm(text: string, searchTerm: string): string {
        if (!searchTerm || !text) {
            return text;
        }

        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    /**
     * Zoom to currently visible markers
     */
    private zoomToVisibleMarkers(): void {
        const clusterSource = this.clusterLayer?.getSource();
        const vectorSource = clusterSource?.getSource();

        if (!vectorSource) {
            return;
        }

        const visibleFeatures = vectorSource.getFeatures().filter((feature: Feature<Point>) => !feature.get('hidden'));

        if (visibleFeatures.length > 0) {
            this.zoomToExtent(visibleFeatures);
        }
    }

    /**
     * Handle zoom control button click, zoom or dezoom the map
     */
    private handleZoomControlButtonClick(e): void {
        const { maxZoom, minZoom } = this.options;
        const { target } = e;
        const view = this.olInstance!.getView();
        const zoom = view.getZoom()!;

        if (hasClass(target, 'ol-zoom-in') && Math.floor(zoom) !== maxZoom) {
            view.animate({
                zoom: zoom + 1,
                duration: 250,
            });
        }

        if (hasClass(target, 'ol-zoom-out') && Math.floor(zoom) !== minZoom) {
            view.animate({
                zoom: zoom - 1,
                duration: 250,
            });
        }
    }

    /**
     * clear all results state
     */
    private clearResultsState(): void {
        this.searchResults.forEach(item => {
            removeClass(item, 'is-active');
            item.removeAttribute('aria-pressed');
        });
    }

    /**
     * Change result item state to "active"
     * @param uid - target uid of marker/result
     */
    private setActiveStateToResultElement(uid: string): void {
        const targetResultItem = this.searchResults.find(item => item.getAttribute('data-uid') === uid);

        if (targetResultItem) {
            addClass(targetResultItem, 'is-active');
            targetResultItem.setAttribute('aria-pressed', 'true');
        }
    }

    /**
    * Change result item state to "non active"
    * @param uid - target uid of marker/result
    */
    private removeActiveStateFromResultElement(uid: string): void {
        const targetResultItem = this.searchResults.find(item => item.getAttribute('data-uid') === uid);

        if (targetResultItem) {
            removeClass(targetResultItem, 'is-active');
            targetResultItem.removeAttribute('aria-pressed');
        }
    }

    /**
     * Manipulate marker padding according to responsive
     */
    private getMarkerFocusPadding(): void {
        if (window.responsive === 'smartphone' && window.innerWidth < 441) {
            this.markerFocusPadding = [120, 50, 50, 50];
        } else {
            this.markerFocusPadding = [50, 500, 50, 50];
        }
    }

    /**
     * Set context elements for map.
     * @private
     */
    private setContextElements(): void {
        const { popupEl, contextEl } = this.options;

        this.element = StratisMap.getElement(this.selector, null, true);

        if (typeof contextEl !== 'boolean') {
            this.contextEl = contextEl instanceof HTMLElement
                ? contextEl
                : this.element!.closest(contextEl);
        }

        if (!this.contextEl) {
            this.contextEl = this.element;
        }

        if (typeof popupEl !== 'boolean') {
            this.popupEl = StratisMap.getElement(popupEl, this.contextEl);
        }
    }

    /**
     * Get other DOMElements
     */
    private getDOMElements(): void {
        const { searchResults, filterTemplateColumn, searchToggle, filterToggle, zoomControll } = this.options.selectors;

        this.filterColumn = document.querySelector<HTMLElement>(filterTemplateColumn)!;
        this.searchToggle = document.querySelector<HTMLElement>(searchToggle)!;
        this.filterToggle = document.querySelector<HTMLElement>(filterToggle)!;
        this.searchResults = [...document.querySelectorAll<HTMLElement>(searchResults)];
        this.zoomControll = this.element!.querySelector<HTMLElement>(zoomControll)!;
    }

    /**
     * Get options from data-map-options attribute
     */
    private handleDataMapOptions(): void {
        const dataOptions = this.element!.getAttribute('data-map-options');

        if (dataOptions) {
            try {
                const options = JSON.parse(dataOptions);
                const convertedOptions = convertPrimitiveKeys(options);
                this.options = extendDefaults(this.options, convertedOptions);
            } catch (err) {
                StratisMap.emitError((err as any).message);
            }
        }
    }

    /**
     * Get map data from HTML
     * @private
     */
    private getDataFromHTML(): void {
        const layers = [...this.contextEl!.querySelectorAll<HTMLElement>('.js-map-layer')];
        const markers = [...this.contextEl!.querySelectorAll<HTMLElement>('.js-map-marker')];

        if (layers.length) {
            this.layers = layers.map(el => {
                const layer = StratisMapLayer.createFromHTML(el);

                if (layer) {
                    this.layersQueue.push(layer.getProp('uid'));
                }

                return layer;
            }) as StratisMapLayer[];
        }

        if (markers.length) {
            this.markers = markers.map(StratisMapMarker.createFromHTML) as StratisMapMarker[];
        }
    }

    /**
     * Run map update.
     * @param {StratisMapMarker[]} markers - array of StratisMapMarkers
     * @param {StratisMapLayer[]} layers - array of StratisMapLayer
     * @private
     */
    private async runUpdate(markers: StratisMapMarker[], layers: StratisMapLayer[]): Promise<void> {
        this.isReady = false;

        if (markers.length && this.clusterLayer) {
            const olFeatures = markers.map(marker => marker.getOlFeature()) as Feature<Point>[];
            this.clusterLayer.updateSource(olFeatures);
        }

        if (layers.length) {
            this.layers.forEach(layer => {
                if (!(layer instanceof TileLayer) && !(layer instanceof Cluster)) {
                    this.olInstance!.removeLayer(layer.getOlLayer());
                }
            });

            layers.forEach(layer => {
                this.addLayer(layer);
            });
        }

        await this.checkMapReadyState();
        this.options.afterUpdate(markers, layers);
    }

    /**
     * Create cluster layer for map.
     * @private
     */
    private createClusterLayer(): void {
        const { defaultMarker, selectedMarker, cluster, selectedMarkerBorder, markerColor, selectedMarkerColor } = this.options;
        const layerOptions = {
            type: 'Cluster',
            features: this.markers.map(el => el.getOlFeature()),
            styleProps: {
                defaultMarker,
                selectedMarker,
                markerColor,
                selectedMarkerColor,
            },
        };

        if (cluster) {
            layerOptions.styleProps['cluster'] = cluster;
        }

        if (selectedMarkerBorder) {
            const setOpacity = (hex: StringBool, alpha: number): string => `${hex}${Math.floor(alpha * 255).toString(16).padStart(2, '0')}`;
            layerOptions.styleProps['selectedMarkerBorder'] = selectedMarkerBorder;
            layerOptions.styleProps['selectedMarkerBorder'].color = setOpacity(selectedMarkerColor, 0.5);
        }

        this.clusterLayer = new StratisMapLayer(layerOptions);
        this.layers.push(this.clusterLayer);
    }

    /**
     * Create default map layers
     * @private
     */
    private createDefaultLayers(): void {
        const { defaultLayers, useGeoportal, geoportalKey, bingMapsKey } = this.options;

        defaultLayers.forEach(layer => {
            if (useGeoportal && layer.type === LayerTypes.geoportal) {
                this.defaultLayers.push(new StratisMapLayer({
                    ...layer,
                    key: geoportalKey,
                }));
            } else if (!useGeoportal && layer.type !== LayerTypes.geoportal) {
                if (layer.type === LayerTypes.bing) {
                    layer.key = bingMapsKey;
                }

                this.defaultLayers.push(new StratisMapLayer(layer));
            }
        });
    }

    /**
     * Set map data.
     * @private
     */
    private setData(): void {
        this.getDataFromHTML();
        this.createDefaultLayers();
        this.createClusterLayer();
    }


    /**
     * Add a11y attrs to the canvas layer.
     * @private
     */
    private canvasHandler(): void {
        const { canvasAriaLabel, selectors } = this.options;
        this.canvasLayer = [...this.element!.querySelectorAll(selectors.canvasLayer)] as HTMLElement[];
        this.canvasLayer?.forEach((canvas, index) => {
            if (index) {
                setAttributes(canvas, {
                    'role': 'img',
                    'aria-hidden': true,
                    'loading': 'lazy',
                });
            } else {
                setAttributes(canvas, {
                    'role': 'img',
                    'aria-label': canvasAriaLabel,
                    'tabindex': '-1',
                    'loading': 'lazy',
                });
            }
        });
    }

    /**
     * Check when layers are fully loaded.
     * @private
     */
    private checkLayersState(): Promise<void> {
        return new Promise(resolve => {
            this.layers.forEach(layer => {
                layer.whenReady((olLayer, olSource, olFeatures) => {
                    const idx = this.layersQueue.findIndex(uid => uid === layer.getProp('uid'));

                    this.olFeatures.push(...olFeatures);

                    if (idx >= 0) {
                        this.layersQueue.splice(idx, 1);
                    }

                    if (!this.layersQueue.length) {
                        resolve();
                    }
                });
            });
        });
    }

    /**
     * Check map reactions on load state.
     * @private
     */
    private checkLoadingBehavior(): void {
        this.isReady = true;

        const { zoomToContentOnLoad, classList, showPopupForSingleMarker } = this.options;
        const isSingleFeature = this.markers.length === 1 && this.layers.length === 1;

        if (zoomToContentOnLoad) {
            this.zoomOnContent();
        }

        if (showPopupForSingleMarker && isSingleFeature) {
            const params = this.markers[0].getOlFeature()!.getProperties() as any;
            this.showPopup(params);
        }

        removeClass(this.contextEl!, classList.loading);

        this.contextEl!.dispatchEvent(this.events.mapReady);
        this.options.afterInit(this.element as HTMLElement, this);
    }

    /**
     * Check if map fully loaded
     * @private
     */
    private async checkMapReadyState(): Promise<void> {
        if (!this.isReady) {
            await this.checkLayersState();
            this.checkLoadingBehavior();
        } else {
            this.checkLoadingBehavior();
        }
    }

    /**
     * Save instance to DB.
     * @private
     */
    private saveToDB(): void {
        this.element!.setAttribute('data-map-db-id', this.id);
        DB.save(this.constructor.name, this);
    }

    /**
     * Create map zoom controls.
     * @private
     */
    private createScaleLinesControls(): void {
        const scaleLine = new ScaleLine();

        this.controls.push(scaleLine);
    }

    /**
     * Get MouseWheel interaction and store in class property.
     * @private
     */
    private getMouseWheelInteraction(): void {
        this.interactions.forEach(interaction => {
            if (interaction instanceof MouseWheelZoom) {
                this.mouseWheelInteraction = interaction;
            }
        });
    }

    /**
     * Remove MouseWheel interaction from map.
     * @private
     */
    private removeMouseWheelInteraction(): void {
        if (this.mouseWheelInteraction) {
            this.interactions.remove(this.mouseWheelInteraction);
        }
    }

    /**
     * Set default markers overlay
     * @private
     */
    private setMarkerOverlay(): void {
        this.overlays.push(this.markerOverlay.getOlOverlay());
    }

    // Attributions (copyright)
    private setAttribution(): void {
        this.controls.push(new Attribution({ collapsed: false }));
    }

    /**
     * Set map control elements.
     * @private
     */
    private setControls(): void {
        this.setAttribution();
        this.createScaleLinesControls();
    }

    /**
     * Set map interactions.
     * @private
     */
    private setInteractions(): void {
        const { mouseWheelZoom } = this.options;

        this.getMouseWheelInteraction();

        if (!mouseWheelZoom) {
            this.removeMouseWheelInteraction();
        }
    }

    /**
     * Set map overlays.
     * @private
     */
    private setOverlays(): void {
        this.setMarkerOverlay();
    }

    /**
     * Handle map features at pixel
     * @private
     */
    // eslint-disable-next-line sonarjs/cognitive-complexity
    private handleFeaturesAtPixel(features: Feature<Point>[] = []): void {
        const { zoomToExtentOnMarkerClick, zoomToExtentOnClusterClick } = this.options;

        if (features.length) {
            features.forEach(feature => {
                const markers = feature.get('features') || [feature];

                if (markers?.length) {
                    const isSingleMarker = markers.length === 1;

                    this.resetMarkers(false);
                    // this.selectMarker(markers[0].get('uid'), false);

                    if (isSingleMarker) {
                        const [marker] = markers;
                        const isInteractionDisabled = marker.get('isInteractionDisabled');

                        if (isInteractionDisabled) {
                            return;
                        }

                        this.showPopup(markers[0].getProperties());
                    }

                    if (isSingleMarker && zoomToExtentOnMarkerClick || !isSingleMarker && zoomToExtentOnClusterClick) {
                        this.zoomToExtent(markers);
                    }
                }
            });
        }
    }

    /**
     * Get all uids from params array.
     * @private
     */
    private getUidsFromParams(params: MapRequestParams | MapRequestParams[]): MapRequestParams {
        const paramsArray = Array.isArray(params) ? params : [params];
        const uidsArray = paramsArray.map(feature => Number(feature.uid));
        // If we have a bunch of features we should request only one
        const [paramsToRequest] = paramsArray;
        // Override single uid with all features uids.
        paramsToRequest.uid = uidsArray.join(',');

        return paramsToRequest;
    }

    /**
     * Request popup from BE.
     * @param {object} params - data to send on BE.
     */
    private async request(params: MapRequestParams): Promise<any> {
        try {
            const url = parseUrl(this.options.BERequestUrl, params);
            const response = await fetch(url);

            return await response.text();
        } catch (error) {
            throw new Error(error as any);
        }
    }

    /**
    * Check If an element is visible in the Viewport vertically
    * @param {HTMLElement} element - active toggle inside tabs and accordion
    */
    private isInViewportVertically(element): boolean {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
        );
    }

    /**
     * Filter and show popup from list of popups for manual point
     */
    private showManualPopup(uid: string): void {
        const { hidden, popup } = this.options.classList;
        const manualPopups = [...this.popupEl!.querySelectorAll<HTMLElement>(`.${popup}`)];
        let popupToShow;

        addClass(this.popupEl!, hidden);

        manualPopups.forEach(singlePopup => {
            const popupUid = singlePopup.getAttribute('data-uid');
            addClass(singlePopup, hidden);

            if (uid === popupUid) {
                popupToShow = singlePopup;
            }
        });

        if (popupToShow) {
            removeClass(popupToShow, hidden);
            removeClass(this.popupEl!, hidden);
            this.selectMarker(uid);

            if (this.isInViewportVertically(getFirstFocusableElement(this.popupEl!))!) {
                // Focus on close button
                getFirstFocusableElement(this.popupEl!)!.focus();
            }
        }

        this.getIdForTitle();

        this.contextEl!.dispatchEvent(this.events.popupShow);
    }

    /**
     * Show ajax popup
     * @param params
     * @private
     */
    private async showAsyncPopup(params: MapRequestParams): Promise<void> {
        const { loading, hidden } = this.options.classList;

        addClass(this.element!, loading);

        const data = await this.request(params);
        this.options.onDataFetch(data);

        removeClass(this.element!, loading);

        if (this.popupEl) {
            this.popupEl.innerHTML = data;
        }

        removeClass(this.popupEl!, hidden);
        this.selectMarker(params.uid);

        this.contextEl!.dispatchEvent(this.events.popupShow);

        this.getIdForTitle();

        if (this.options.disableFocusOnPopup) {
            this.options.disableFocusOnPopup = false;
            return;
        }

        // Focus on close button
        setTimeout(() => {
            if (this.isInViewportVertically(getFirstFocusableElement(this.popupEl!)!)) {
                getFirstFocusableElement(this.popupEl!)!.focus();
            }
        }, 0);
    }

    /**
     * Redirect user to other service if we have troubles with route construction.
     * @private
     */
    private routeFallbackRedirect(): void {
        if (this.routeFallbackLink) {
            window.open(this.routeFallbackLink);
        }
    }

    /**
     * Remove old route from map if it exists.
     * @private
     */
    private resetActiveRoute(): void {
        if (this.activeRoute) {
            this.olInstance!.removeLayer(this.activeRoute.getOlLayer());
            this.activeRoute = null;
        }
    }

    /** * Get id for title
     * add aria-labelledby with the same value as id title
     * */
    public getIdForTitle(): void {
        const titleId = this.popupEl?.querySelector('.map-popup__title')?.id;
        const isMapPage = hasClass(document.body, 'map-page');

        if (titleId && isMapPage) {
            this.popupEl?.setAttribute('aria-labelledby', titleId);
        }
    }

    /**
     * Request map route data from API.
     * @param startCoords - start point coordinates
     * @param endCoords - end point coordinates
     * @private
     */
    private async requestRouteData(startCoords: Coordinates, endCoords: Coordinates): Promise<void> {
        const startCoordsParam = encodeURIComponent(`${startCoords[0]},${startCoords[1]}`);
        const endCoordsParam = encodeURIComponent(`${endCoords[0]},${endCoords[1]}`);
        const url = this.routeRequestUrl
            .replace('{start}', startCoordsParam)
            .replace('{end}', endCoordsParam);

        const response = await axios.get(url);
        return response.data;
    }

    /**
     * Get data for route construction and construct route on success result.
     * @private
     */
    private getRouteData(): void {
        const geolocation = new Geolocation({
            trackingOptions: {
                enableHighAccuracy: true,
            },
            tracking: true,
        });

        geolocation.on('change:position', () => {
            const startCoods = geolocation.getPosition() as [number, number];
            const endCoords = transform(this.activeMarker!.getGeometry()!.getCoordinates(), 'EPSG:3857', 'EPSG:4326') as [number, number];
            this.createMapRoute(startCoods, endCoords);
        });

        geolocation.on('error', () => {
            alert('Sorry you denied Geolocation service');
            this.routeFallbackRedirect();
        });
    }

    /**
     * Create map route and add it on Map.
     * @param startCoords - start point coordinates
     * @param endCoords - end point coordinates
     * @private
     */
    private async createMapRoute(startCoords: Coordinates, endCoords: Coordinates): Promise<void> {
        try {
            const routeData = await this.requestRouteData(startCoords, endCoords) as any;
            const { route } = this.options;
            const styleProps = {} as any;

            if (route) {
                styleProps.route = route;
            }

            this.resetActiveRoute();

            this.activeRoute = new StratisMapLayer({
                type: 'geojson',
                dataProjection: routeData.crs,
                routeGeometry: {
                    type: 'FeatureCollection',
                    features: [routeData.geometry],
                },
                styleProps,
            });
            const olLayer = this.activeRoute.getOlLayer();

            this.olInstance!.addLayer(olLayer);
            this.zoomToExtent(olLayer);
        } catch (error) {
            console.error('Sorry you are outside of France, route construction works only for France region');
            this.routeFallbackRedirect();
        }
    }

    /**
     * Map popup handler.
     * {Event} BrowserEvent - browser event.
     */
    private popupHandler(event): void {
        const { popupShowAction, popupHideAction } = this.options.classList;
        const targetElement = event.target;

        if (hasClass(targetElement, popupHideAction)) {
            event.preventDefault();
            this.hidePopup();

            if (this.filterColumn) {
                this.moveFocusToOpenButton();
            } else {
                this.moveFocusToCanvas();
            }

            return;
        }

        if (hasClass(targetElement, popupShowAction)) {
            event.preventDefault();
            const popupParams = { ...targetElement.dataset };

            if (popupParams.uid) {
                this.showPopup(popupParams);
            }
        }
    }

    /**
     * Move focus to open button
     */
    private moveFocusToOpenButton(): void {
        const { templateColumn } = this.options.selectors;
        const uid = this.popupEl!.getAttribute('id');
        const targetResult = this.searchResults.find(item => item.getAttribute('data-uid') === uid);
        const templateColumnElement = targetResult?.closest(templateColumn) as HTMLElement;

        if (hasClass(templateColumnElement, 'is-active')) {
            targetResult!.focus();
        } else if (this.filterColumn.getAttribute('aria-hidden') === 'false') {
            this.searchToggle.focus();
        } else {
            this.filterToggle.focus();
        }
    }

    /**
     * Move focus to the canvas
     */
    private moveFocusToCanvas(): void {
        if (this.canvasLayer) {
            this.canvasLayer[this.canvasLayer.length - 1].focus();
        }
    }

    /**
     * Overlay handler func.
     */
    private markerOverlayHandler(event): void {
        const features = this.olInstance!.getFeaturesAtPixel(event.pixel) as Feature<Point>[];
        const mapTarget = this.olInstance!.getTarget();
        const zoomLevel = this.olInstance!.getView().getZoom();
        const markers = features[0]?.get('features');
        const isCluster = markers?.length > 1;
        const isOverlayHoverTarget = event.originalEvent.target.closest('.ol-overlay-container');
        const isClusterConditionDone = isCluster && zoomLevel === this.options.maxZoom;

        this.isScrollActivated = !isOverlayHoverTarget;
        if (isClusterConditionDone && !isOverlayHoverTarget || features.length && !isOverlayHoverTarget) {
            (mapTarget! as HTMLElement).style.cursor = 'pointer';
            this.markerOverlay.show(features, event.coordinate);
        } else if (!isOverlayHoverTarget) {
            (mapTarget! as HTMLElement).style.cursor = '';
            this.markerOverlay.hide();
        }
    }

    /**
    * Map MouseWheel handler
    * @private
    */
    private mouseWheelHandler(): void {
        if (this.mouseWheelInteraction) {
            this.mouseWheelInteraction.setActive(this.isScrollActivated);
        }
    }

    /**
     * Feature handler function.
     * @param event
     * @private
     */
    private featureHandler(event): void {
        const { onMarkerClick, disableMarkerClick } = this.options;

        if (disableMarkerClick) {
            return;
        }

        const featuresAtPixel = this.olInstance!.getFeaturesAtPixel(event.pixel);

        onMarkerClick(this, featuresAtPixel as Feature<Point>[]);
        this.handleFeaturesAtPixel(featuresAtPixel as Feature<Point>[]);
    }

    private routeHandler(event): void {
        const targetEl = event.target;

        if (targetEl && hasClass(targetEl, 'js-map-route')) {
            event.preventDefault();
            this.routeFallbackLink = event.target.href;
            this.getRouteData();
        }
    }

    /**
     * Create custom map events.
     */
    private setCustomEvents(): void {
        const { olInstance, element } = this;

        Object.keys(this.events).forEach(event => {
            this.events[event] = new CustomEvent(this.events[event], {
                bubbles: true,
                cancelable: false,
                detail: {
                    olInstance,
                    element,
                },
            });
        });
    }

    /**
     * Resize handler for screen
     */
    private resizeHandler(): void {
        this.getMarkerFocusPadding();
        this.updateSize.bind(this, 100);
    }

    /**
     * Add events to map.
     * @private
     */
    private addMapEvents(): void {
        const wheelInteractionName: any = 'wheel';
        const isPageListWithMap = document.body.classList.contains('is-list-with-filter');
        this.olInstance!.on(wheelInteractionName, this.$mouseWheelHandler);
        if (isPageListWithMap) {
            this.olInstance!.on('click', this.handleMarkerAndClusterClick.bind(this));
            this.olInstance!.on('pointermove', this.markerHoverHandlerList.bind(this));
        } else {
            this.olInstance!.on('click', this.$featureHandler);
            this.olInstance!.on('pointermove', this.$markerOverlayHandler);

        }
        this.olInstance!.on('rendercomplete', this.$canvasHandler);

        if (this.contextEl && this.popupEl) {
            this.contextEl.addEventListener('click', this.$popupHandler);
            this.popupEl.addEventListener('click', this.routeHandler.bind(this));
        }

        if (this.zoomControll) {
            this.zoomControll.addEventListener('click', this.$handleZoomControlButtonClick);
        }

        window.addEventListener('resize', this.$resizeHandler);
    }

    /**
     * Render map to DOM.
     * @private
     */
    private renderMap(): void {
        addClass(this.contextEl!, this.options.classList.loading);

        const { defaultZoom, maxZoom, defaultLat, defaultLon, beforeInit, padding } = this.options;

        beforeInit(this.element as HTMLElement);

        this.olInstance = new Map({
            target: this.element!,
            view: new View({
                center: this.setCoords(defaultLon, defaultLat),
                zoom: defaultZoom,
                padding,
                maxZoom,
            }),
            layers: [
                ...StratisMap.extractOlLayers(this.defaultLayers),
                ...StratisMap.extractOlLayers(this.layers),
            ],
            controls: this.controls,
            interactions: this.interactions,
            overlays: this.overlays,
        });

        this.checkMapReadyState();
        this.addMapEvents();
    }

    /**
     * Init instance.
     * @private
     */
    private init(): void {
        try {
            this.created = true;
            this.setContextElements();
            this.getDOMElements();
            this.saveToDB();
            this.handleDataMapOptions();
            this.setData();
            this.setControls();
            this.setInteractions();
            this.setOverlays();
            window.addEventListener('load', this.getMarkerFocusPadding.bind(this));
            this.setCustomEvents();
            this.renderMap();
        } catch (err) {
            console.error((err as any).message);
        }
    }

    private handleMarkerAndClusterClick(event): void {
        const { zoomToExtentOnClusterClick } = this.options;

        // Get features at clicked position
        const featuresAtPixel = this.olInstance!.getFeaturesAtPixel(event.pixel);
        const clickedFeature = featuresAtPixel && featuresAtPixel[0];

        if (!clickedFeature) {
            return;
        }

        // Check if it's a marker by accessing the custom "marker" property
        const isMarker = clickedFeature.get('features').length === 1;

        // Handle Marker Click (Zoom and Navigate to Google)
        if (isMarker) {
            const markerGeometry = clickedFeature.getGeometry();
            if (markerGeometry instanceof Point) {
                // Zoom to the clicked marker
                this.zoomToFeatureWithZoomLevel(clickedFeature);

                // Navigate to Google.com after zoom
                // window.location.href = 'https://www.google.com';
            }
        }

        // Handle Cluster Click (Alert when zoomed in completely)
        if (!isMarker) {
            const zoomLevel = this.olInstance!.getView().getZoom();

            if (zoomLevel && zoomLevel >= 14) {
                const mapClusterElements = document.querySelector('.map-cluster-elements');
                mapClusterElements!.classList.remove('is-hidden');
            }

            if (zoomToExtentOnClusterClick) {
                this.zoomToFeatureWithZoomLevel(featuresAtPixel);
            }
        }
    }

    /**
 * Handle hover on marker or cluster.
 */
    private markerHoverHandlerList(event): void {
        const features = this.olInstance!.getFeaturesAtPixel(event.pixel) as Feature<Point>[];
        const mapTarget = this.olInstance!.getTarget();
        const zoomLevel = this.olInstance!.getView().getZoom();
        const markers = features[0]?.get('features');
        const isCluster = markers?.length > 1;
        const isOverlayHoverTarget = event.originalEvent.target.closest('.ol-overlay-container');
        const isClusterConditionDone = isCluster && zoomLevel === this.options.maxZoom;

        this.isScrollActivated = !isOverlayHoverTarget;
        if (isClusterConditionDone && !isOverlayHoverTarget || features.length && !isOverlayHoverTarget) {
            (mapTarget! as HTMLElement).style.cursor = 'pointer';
            if (!isCluster) {
                this.markerOverlay.show(features, event.coordinate);
            }
        } else if (!isOverlayHoverTarget) {
            (mapTarget! as HTMLElement).style.cursor = '';
            this.markerOverlay.hide();
        }
    }
}
