/**
 * Transform cookie string to object.
 * @param cookies {string} - cookie string
 * @return {object}
 */
export const cookiesToObject = (cookies): object => {
    return cookies
        .split('!')
        .filter(cookie => cookie !== '')
        .reduce((current, next) => {
            const [key, value] = next.split('=');
            current[key] = value;
            return current;
        }, {});
};

/**
 * Transform object to query string
 * @param object
 * @return string
 */
export const objectToQueryString = (object): string => {
    const params = Object.keys(object)
        .map(key => {
            return object[key] ? `${key}=${object[key].toString()}` : '';
        })
        .filter(key => key !== '')
        .join('&');

    return params.length ? `?${params}` : '';
};
