.edition-item {
    $this: &;

    @extend %link-block-context;
    display: flex;
    margin-left: -196px;
    width: 835px;

    @include breakpoint(medium down) {
        margin-left: 0;
        width: 100%;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
        padding-top: 16px;
        text-align: center;
    }

    &__image-wrapper {
        @include size(276px, 392px);
        box-shadow: 0 0 20px rgba($color-black, 0.16);
        display: inline-block;
        flex-shrink: 0;
        transform: rotate(-5.14deg);
        z-index: 1;

        @include breakpoint(medium down) {
            @include size(175px, 247px);
        }

        @include breakpoint(small down) {
            @include size(160px, 227px);
            margin: 0 auto;
            padding-bottom: 4px;
        }

        img {
            @include object-fit;
            @include size(100%);
            background-color: $color-white;
        }
    }

    &__content {
        background-color: $color-white;
        display: none;
        flex-grow: 1;
        padding: 99px 0 0 64px;
        position: relative;
        width: 1%;

        @include breakpoint(medium down) {
            padding: 10px 0 0 27px;
        }

        @include breakpoint(small down) {
            padding: 30px 0 0;
            width: 100%;
        }

        &::after {
            @include absolute(0, null, 0, 0);
            @include size(50px, 100%);
            background-color: $color-white;
            content: "";
            transform: translateX(-100%);

            @include breakpoint(medium down) {
                content: none;
            }
        }

        .swiper-slide-active & {
            display: block;
        }
    }

    &__category {
        margin-bottom: 13px;

        @include breakpoint(small down) {
            margin-bottom: 19px;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus-visible {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &__subtitle {
        @include font(null, 3.7rem, var(--fw-normal));
        display: block;
        line-height: 4rem;

        @include breakpoint(medium down) {
            @include font(null, 2.5rem, var(--fw-medium));
            line-height: 5rem;
        }

        @include breakpoint(medium only) {
            margin-top: 40px;
        }

        @include breakpoint(small down) {
            font-size: 2.2rem;
            line-height: 2.4rem;
        }
    }

    &__teaser {
        @include breakpoint(medium down) {
            margin-top: 13px;
            max-width: 328px;
        }

        @include breakpoint(small down) {
            margin-top: 19px;
        }
    }

    &__publication {
        margin-top: 17px;

        @include breakpoint(small down) {
            margin-top: 8px;
        }

        &.publication[class] {
            &::before {
                @include breakpoint(small down) {
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
    }

    &__document {
        @include font(var(--typo-1), 1.2rem, var(--fw-normal));
        color: $color-3--4;
        display: block;
        margin-top: 13px;
        padding-left: 45px;
        position: relative;
        text-transform: uppercase;

        @include breakpoint(small down) {
            padding-left: 0;
        }

        &::before {
            @include absolute(7px, null, null, 0);
            @include size(35px, 4px);
            background-color: var(--color-2--1);
            content: '';

            @include breakpoint(small down) {
                content: none;
            }
        }
    }

    &__number {
        @include font(null, null, var(--fw-bold), normal);
        display: block;
    }

    &__actions {
        margin-top: 15px;
        position: relative;
        z-index: 3;

        @include breakpoint(medium down) {
            margin-top: 12px;
        }

        @include breakpoint(small down) {
            display: block;
            margin-top: 20px;
        }

        .document-actions {
            flex-direction: row;

            @include breakpoint(small down) {
                align-items: center;
                justify-content: center;
            }

            &__item {
                &:last-child {
                    margin-left: 37px;
                }

                .btn {
                    color: var(--color-1--1);
                    font-weight: var(--fw-medium);
                    padding: 5px 0;

                    span:not([class*=fa-]) {
                        letter-spacing: 0;
                    }
                }
            }
        }
    }
}
