{%- from 'views/core-components/section.njk' import Section -%}
{%- from 'views/core-components/title.njk' import TitlePrimary, TitleSecondary -%}
{%- from 'components/login/login.njk' import Login -%}
{%- from 'components/quicklinks/quicklinks.njk' import QuickLinksBlock -%}
{%- from 'components/online-steps/online-steps.njk' import OnlineSteps -%}

{#
    DemarchesHome template.
    @param {string} titleText - section title
#}
{%- macro DemarchesHome(
    titleText = 'Que peut-on faire pour vous'
) -%}
    {% call Section(className = 'demarches', container = 'demarches__container') %}
        <div class="section__title demarches__title">
            {{ TitlePrimary(
                className = 'is-center',
                text = titleText
            ) }}
        </div>
        <div class="section__content">
            <div class="flex-row">
                <div class="col-lg-4 col-md-12 col-xs-12">
                    <section class="online-steps-section">
                        {{ TitleSecondary(
                            className = 'demarches__sub-title',
                            text = 'Démarcher en ligne',
                            tag = 'h3'
                        ) }}
                        {{ OnlineSteps(imageSizes = false) }}
                    </section>
                </div>
                <div class="col-lg-4 col-md-12 col-xs-12">
                    <section class="quicklinks-section">
                        {{ TitleSecondary(
                            className = 'demarches__sub-title',
                            text = 'Accès rapides',
                            tag = 'h3'
                        ) }}
                        {{ QuickLinksBlock(ariaLabelText = 'Accès rapides') }}
                    </section>
                </div>
                <div class="col-lg-4 col-md-12 col-xs-12">
                    <section class="login-section">
                        {{ TitleSecondary(
                            className = 'demarches__sub-title',
                            text = 'Mon compte',
                            tag = 'h3'
                        ) }}
                        {{ Login() }}
                    </section>
                </div>
            </div>
        </div>
    {% endcall %}
{%- endmacro -%}
