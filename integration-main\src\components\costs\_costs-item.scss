$colors: (
    var(--color-1--1),
    var(--color-2--1),
    var(--color-2--3),
);

.cost-item {
    $this: &;

    display: flex;

    @include breakpoint(medium down) {
        flex-direction: column;
    }

    &__description {
        font-size: 2rem;
        line-height: 3.4rem;
        margin: 0 24px 20px 0;

        @include breakpoint(large only) {
            max-width: 384px;
        }

        @include breakpoint(medium down) {
            font-size: 1.8rem;
            line-height: 3rem;
            margin: 0 0 50px;
        }

        @include breakpoint(small down) {
            margin-bottom: 36px;
        }
    }

    &__chart {
        display: flex;

        @include breakpoint(medium down) {
            justify-content: center;
        }

        @include breakpoint(small down) {
            display: block;
        }
    }

    &__chart-cost {
        font-size: 2rem;
        margin-right: 20px;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }

        @include breakpoint(small down) {
            font-size: 1.1rem;
            margin: 0 0 15px;
            text-align: center;
        }

        strong {
            font-size: 5rem;
            margin-right: 10px;
            vertical-align: middle;

            @include breakpoint(medium down) {
                font-size: 4.6rem;
            }

            @include breakpoint(small down) {
                font-size: 3rem;
            }
        }
    }

    &__legend {
        padding-left: 50px;

        @include breakpoint(medium down) {
            padding-left: 20px;
        }

        @include breakpoint(small down) {
            align-items: center;
            display: flex;
            flex-direction: column;
            padding-left: 0;
        }
    }

    &__list[class] {
        border-left: 1px solid $color-3--3;
        margin-left: 32px;
        padding-left: 48px;

        @include breakpoint(small down) {
            margin-left: 10px;
            padding-left: 30px;
        }
    }

    &__title {
        align-items: center;
        display: flex;
        font-size: 2rem;
        font-weight: var(--fw-normal);
        margin-bottom: 14px;

        strong {
            font-size: 4.5rem;
            margin: 0 14px 0 24px;
        }

        @include breakpoint(small down) {
            justify-content: center;
        }
    }

    &__element {
        align-items: center;
        display: flex;
        font-size: 2rem;
        margin-bottom: 15px;
        position: relative;

        &:last-of-type {
            margin-bottom: 0;
        }
    }

    &__colorbox {
        @include size(30px);
        border-radius: 50%;
        display: block;
        flex-shrink: 0;
        margin-right: 17px;
    }

    &__canvas {
        @include size(282px);

        @include breakpoint(small down) {
            margin: 0 auto 30px;
        }
    }

    &__amount {
        flex-shrink: 0;
        font-size: 3.5rem;
        line-height: 1;
        margin-right: 1.4rem;
    }

    @for $i from 1 through length($colors) {
        &__element:nth-child(#{$i}) #{$this}__colorbox {
            background-color: nth($colors, $i);
        }
    }
}
