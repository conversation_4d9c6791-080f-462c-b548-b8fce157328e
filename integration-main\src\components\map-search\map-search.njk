{%- from 'views/core-components/icon.njk' import Icon -%}
{%- from 'views/core-components/button.njk' import Button -%}

{#
    MapSearch default settings.
    @param {string} placeholder - placeholder text for search input
    @param {string} label - label text for search input
    @param {boolean} showCount - whether to show results count
    @param {string} modifier - additional CSS modifier class
#}
{% set MapSearchDefaults = {
    placeholder: 'Rechercher sur la carte...',
    label: 'Rechercher',
    showCount: true,
    modifier: '',
    id: 'map-search-' + Helpers.unique()
} %}

{#
    MapSearch component for dynamic map filtering
    @param {object} settings - component settings
#}
{% macro MapSearch(settings = {}) %}
    {% set params = Helpers.merge(MapSearchDefaults, settings) %}
    
    <div class="map-search js-map-search-container {{ params.modifier }}">
        <input 
            type="search" 
            id="{{ params.id }}"
            class="map-search__input js-map-search-input"
            placeholder="{{ params.placeholder }}"
            autocomplete="off"
            spellcheck="false"
            aria-label="{{ params.label }}"
            aria-describedby="{{ params.id }}-count {{ params.id }}-help"
        >
    </div>
{% endmacro %}

{#
    MapSearchWithFilters - Search component integrated with filter panel
    @param {object} settings - component settings
#}
{% macro MapSearchWithFilters(settings = {}) %}
    {% set filterSettings = Helpers.merge(settings, {
        placeholder: 'Filtrer les résultats...'
    }) %}
    
    <div class="map-search-filters ghost">
        {{ MapSearch(filterSettings) }}
    </div>
{% endmacro %}
